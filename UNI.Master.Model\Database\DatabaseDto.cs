using System;

namespace UNI.Master.Model.Database
{
    public class DatabaseDto
    {
        public Guid Id { get; set; }
        public Guid InstanceId { get; set; }
        public string Name { get; set; } = null!;
        public string LoginUser { get; set; } = null!;
        public string LoginPassword { get; set; } = null!;
        public string Note { get; set; } = null!;
        public Guid? CurrentVersion { get; set; }
    }
}
