﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AssemblyName>SSG.BLL.Keycloak.Template</AssemblyName>
    <PackageId>SSG.BLL.Yamaha_Training</PackageId>    
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <LangVersion>7.1</LangVersion>
    <RootNamespace>SSG.BLL.Keycloak.Template</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="BusinessServiceInterfaces\INotificationService.cs" />
    <Compile Remove="BusinessService\NotificationService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="9.0.0" />
    <PackageReference Include="AWSSDK.SimpleEmail" Version="3.3.6.4" />
    <PackageReference Include="FastMember" Version="1.5.0" />
    <PackageReference Include="FluentEmail.Mailgun" Version="2.3.1" />
    <PackageReference Include="Google.Apis.Auth" Version="1.51.0" />
    <PackageReference Include="Google.Apis.Docs.v1" Version="1.51.0.2252" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.51.0.2265" />
    <PackageReference Include="Google.Cloud.PubSub.V1" Version="1.0.0" />
    <PackageReference Include="MailKit" Version="2.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="2.0.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="2.2.0" />
    <PackageReference Include="NetBarcode" Version="1.1.0" />
    <PackageReference Include="PdfSharpCore" Version="1.2.6" />
    <PackageReference Include="RestSharp" Version="106.10.1" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Runtime.Serialization.Formatters" Version="4.3.0" />
    <PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SSG.DAL.Keycloak.Template\SSG.DAL.Keycloak.Template.csproj" />
  </ItemGroup>

</Project>
