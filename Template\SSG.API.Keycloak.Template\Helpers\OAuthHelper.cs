﻿using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using RestSharp;
using SSG.API.Keycloak.Template.Authorization;

namespace SSG.API.Keycloak.Template.Helpers
{
    public class OAuthHelper
    {
        public static readonly RestClient Client = new RestClient { Timeout = 60 * 1000 };

        /// <summary>
        /// </summary>
        /// <returns></returns>
        public static async Task<UserToken> GetUserToken(string accessToken, IConfiguration configuration)
        {
            var request = new RestRequest($"{configuration["Jwt:Authority"]}/protocol/openid-connect/token", Method.POST);
            request.AddHeader("content-type", "application/x-www-form-urlencoded");
            request.AddHeader("authorization", accessToken);
            request.AddParameter("grant_type", "urn:ietf:params:oauth:grant-type:uma-ticket");
            request.AddParameter("audience", configuration["Jwt:ClientId"]);
            var userToken = await C<PERSON>.PostAsync<UserToken>(request);

            return userToken;
        }
    }
}
