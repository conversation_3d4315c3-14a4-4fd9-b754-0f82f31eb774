﻿using System;
using System.Threading.Tasks;
using SSG.Common;
using SSG.Model.SHome;

namespace SSG.BLL.Keycloak.Template.BusinessService.HelperService
{
    public class GoogleDriverHomeBillService : GoogleDriverBaseService
    {
        
        #region private-service-doc 
        public async Task<ggDriverFileDownload> UploadBilFile(ggDriverFileStream homestream)
        {
            try
            {
                string viewerUrl = "https://asia-northeast1-sunshine-app-production.cloudfunctions.net";
                string documentName = String.Empty;
                string folderProject = String.Empty;
                string folderUserID = String.Empty;
                string folderMM = String.Empty;
                if (homestream.documentType == 1)
                {
                    documentName = "Hóa đơn";
                }
                else if (homestream.documentType == 2)
                {
                    documentName = "Report";
                }

                folderProject = CheckExistedFolderAndCreate(documentName, FolderServiceGoogledriver.FORLER_SMart_GOOGLEDRIVER, FolderServiceGoogledriver.TEAMDRIVERID);
                //folderProject = CheckExistedFolderAndCreate("SMart", documentName, FolderServiceGoogledriver.TEAMDRIVERID);
                folderUserID = CheckExistedFolderAndCreate(homestream.folderName, folderProject, FolderServiceGoogledriver.TEAMDRIVERID);
                //folderMM = CheckExistedFolderAndCreate(homestream.dDate.ToString("MM"), folderYYYY, FolderServiceGoogledriver.TEAMDRIVERID);
                var file = UploadFromStream(homestream.stream, homestream.fileName, homestream.mimeType, folderUserID);
                var documentId = file.Id;
                //GoogleResultLink
                GoogleResultLink resultLink = new GoogleResultLink();
                //Return download link and WebViewLink
                resultLink = await GetGoogleResultLink(resultLink, documentId, viewerUrl);

                return new ggDriverFileDownload { WebViewLink = resultLink.WebViewLink, WebContentLink = resultLink.PDFDownloadLink };
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        #endregion private-service-doc
    }
}
