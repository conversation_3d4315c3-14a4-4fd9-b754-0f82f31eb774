using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Database;
using UNI.Master.Model.Database;
using UNI.Model;

namespace UNI.Master.DAL.Repositories.Database
{
    public class DatabaseInstanceRepository : UniBaseRepository, IDatabaseInstanceRepository
    {
        public DatabaseInstanceRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        public Task<CommonListPage> GetPageAsync(DatabaseInstanceFilter filter)
        {
            return base.GetPageAsync("sp_database_instance_page", filter, new { filter.Status, filter.Active });
        }

        public Task<DatabaseInstanceInfo> GetInfoAsync(Guid? id)
        {
            return base.GetFieldsAsync<DatabaseInstanceInfo>("sp_database_instance_fields", new { id });
        }

        public Task<BaseValidate> SetInfoAsync(DatabaseInstanceInfo info)
        {
            return base.SetInfoAsync("sp_database_instance_set", info, new { info.Id });
        }

        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return base.DeleteAsync("sp_database_instance_del", new { id });
        }
    }
}
