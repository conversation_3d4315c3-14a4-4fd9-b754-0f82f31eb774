﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RestSharp;
using SSG.DAL.Keycloak.Template.Interfaces.Api;
using SSG.Model.KecloakTemplate.Api;
using SSG.Model.KecloakTemplate.Api.Profile;
using SSG.Common.Extensions;

namespace SSG.DAL.Keycloak.Template.Repositories.Api
{
    public class ApiProfileRepository : IApiProfileRepository
    {
        private readonly ILogger<UserRepository> _logger;
        private readonly IConfiguration _configuration;
        private readonly RestClient _client;
        private readonly string _baseUrl;

        public ApiProfileRepository(RestClient client, IConfiguration configuration, ILogger<UserRepository> logger)
        {
            this._logger = logger;
            this._configuration = configuration;
            this._client = client;
            this._baseUrl = configuration["Clients:Profile:BaseUrl"];
        }

        public async Task<tplApi<tplUserProfile>> GetUserProfile(string accessToken)
        {
            try
            {
                var request = new RestRequest($"{_baseUrl}/api/user/v1/detail");
                request.AddHeader("authorization", $"Bearer {accessToken}");

                var result = await _client.GetApiAsync<tplApi<tplUserProfile>>(request);

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError($"{ex}");
                throw;
            }
        }
    }
}
