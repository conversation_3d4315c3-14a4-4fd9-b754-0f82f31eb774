﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Configuration;
using SSG.DAL.Interfaces;
using SSG.Model;
using SSG.Model.Email;
using SSG.Model.SMS;

namespace SSG.DAL.Repositories
{
    /// <summary>
    /// Notification Repository
    /// </summary>
    /// Author: taint
    /// CreatedDate: 16/11/2016 2:07 PM
    /// <seealso cref="INotificationRepository" />
    public class NotificationRepository : INotificationRepository
    {
        private readonly string _connectionString;

        public NotificationRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("SMSManagerConnection");
        }
        #region Email
        public async Task<long> AddNewEmailGroup(EmailGroup smsGroup, string clientId, string userId)
        {
            const string storedProcedure = "em_Insert_TransactionEmailGroup";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<EmailGroup>(smsGroup));
                    param.Add("@UserID", userId);
                    param.Add("@ClientID", clientId);
                    param.Add("@SendId", 0, DbType.Int64, ParameterDirection.InputOutput);

                    await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return param.Get<long>("@SendId");
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<int> AddNewEmail(List<string> emails, long sendId, string clientId, string userId)
        {
            const string storedProcedure = "em_Insert_Email";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<string>>(emails));
                    param.Add("@UserID", userId);
                    param.Add("@ClientID", clientId);
                    param.Add("@SendId", sendId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ResponseList<IEnumerable<EmailGroup>> GetEmailGroup(PagingParams pagingParams, long sendId)
        {
            const string storedProcedure = "em_Get_TransactionEmailGroup";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    if (pagingParams == null)
                    {
                        pagingParams = new PagingParams();
                    }

                    param.Add("@Offset", pagingParams.Offset);
                    param.Add("@PageSize", pagingParams.PageSize);
                    param.Add("@Search", pagingParams.Search);

                    param.Add("@SortCol", pagingParams.SortCol);
                    param.Add("@SortDir", pagingParams.SortDir);

                    param.Add("@SendId", sendId);
                    param.Add("@Total", 0, DbType.Int32, ParameterDirection.InputOutput);
                    param.Add("@TotalFiltered", 0, DbType.Int32, ParameterDirection.InputOutput);

                    var result = connection.Query<EmailGroup>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return new ResponseList<IEnumerable<EmailGroup>>(result, param.Get<int>("@Total"), param.Get<int>("@TotalFiltered"));

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ResponseList<IEnumerable<EmailEntity>> GetEmail(PagingParams pagingParams, long sendId, long id)
        {
            const string storedProcedure = "em_Get_Email";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    if (pagingParams == null)
                    {
                        pagingParams = new PagingParams();
                    }

                    param.Add("@Offset", pagingParams.Offset);
                    param.Add("@PageSize", pagingParams.PageSize);
                    param.Add("@Search", pagingParams.Search);

                    param.Add("@SortCol", pagingParams.SortCol);
                    param.Add("@SortDir", pagingParams.SortDir);

                    param.Add("@SendId", sendId);
                    param.Add("@Id", id);
                    param.Add("@Total", 0, DbType.Int32, ParameterDirection.InputOutput);
                    param.Add("@TotalFiltered", 0, DbType.Int32, ParameterDirection.InputOutput);

                    var result = connection.Query<EmailEntity>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return new ResponseList<IEnumerable<EmailEntity>>(result, param.Get<int>("@Total"), param.Get<int>("@TotalFiltered"));

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion

        #region SMS
        public async Task<int> AddNewSMS(List<SMSEntity> sms, string clientId, string userId)
        {
            const string storedProcedure = "sm_Insert_SMS";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SMSEntity>>(sms));
                    param.Add("@UserID", userId);
                    param.Add("@ClientID", clientId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ResponseList<IEnumerable<SMS>> GetSMS(PagingParams pagingParams, long? sendId = null, long? id = null)
        {
            const string storedProcedure = "sm_Get_SMS";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    if (pagingParams == null)
                    {
                        pagingParams = new PagingParams();
                    }

                    param.Add("@Offset", pagingParams.Offset);
                    param.Add("@PageSize", pagingParams.PageSize);
                    param.Add("@Search", pagingParams.Search);

                    param.Add("@SortCol", pagingParams.SortCol);
                    param.Add("@SortDir", pagingParams.SortDir);

                    param.Add("@SendId", sendId);
                    param.Add("@Id", id);
                    param.Add("@Total", 0, DbType.Int32, ParameterDirection.InputOutput);
                    param.Add("@TotalFiltered", 0, DbType.Int32, ParameterDirection.InputOutput);

                    var result = connection.Query<SMS>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return new ResponseList<IEnumerable<SMS>>(result, param.Get<int>("@Total"), param.Get<int>("@TotalFiltered"));

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<int> UpdateSMS(List<SMSEntity> sms, string clientId, string userId)
        {
            const string storedProcedure = "sm_Update_SMS";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SMSEntity>>(sms));
                    param.Add("@UserID", userId);
                    param.Add("@ClientID", clientId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<long> AddNewSMSGroup(SMSGroup smsGroup, string clientId, string userId)
        {
            const string storedProcedure = "sm_Insert_TransactionGroup";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<SMSGroup>(smsGroup));
                    param.Add("@UserID", userId);
                    param.Add("@ClientID", clientId);
                    param.Add("@SendId", 0, DbType.Int64, ParameterDirection.InputOutput);

                    await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return param.Get<long>("@SendId");
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ResponseList<IEnumerable<SMSGroup>> GetSMSGroup(PagingParams pagingParams, long? sendId = null)
        {
            const string storedProcedure = "sm_Get_TransactionGroup";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    if (pagingParams == null)
                    {
                        pagingParams = new PagingParams();
                    }

                    param.Add("@Offset", pagingParams.Offset);
                    param.Add("@PageSize", pagingParams.PageSize);
                    param.Add("@Search", pagingParams.Search);

                    param.Add("@SortCol", pagingParams.SortCol);
                    param.Add("@SortDir", pagingParams.SortDir);

                    param.Add("@SendId", sendId);
                    param.Add("@Total", 0, DbType.Int32, ParameterDirection.InputOutput);
                    param.Add("@TotalFiltered", 0, DbType.Int32, ParameterDirection.InputOutput);

                    var result = connection.Query<SMSGroup>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return new ResponseList<IEnumerable<SMSGroup>>(result, param.Get<int>("@Total"), param.Get<int>("@TotalFiltered"));

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<int> UpdateSMSGroup(SMSGroup smsGroup, string clientId, string userId)
        {
            const string storedProcedure = "sm_Update_TransactionGroup";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<SMSGroup>(smsGroup));
                    param.Add("@UserID", userId);
                    param.Add("@ClientID", clientId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion
        #region SMS Template
        public async Task<int> UpdateTemplate(List<SmsTemplate> sms, long? userId)
        {
            const string storedProcedure = "sm_Update_Template";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsTemplate>>(sms));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<int> AddNewTemplate(List<SmsTemplate> sms, long? userId)
        {
            const string storedProcedure = "sm_Insert_Template";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsTemplate>>(sms));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<int> DeleteTemplates(List<SmsTemplate> smsTemplates)
        {
            const string storedProcedure = "cus_Del_Templates";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsTemplate>>(smsTemplates));
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public IEnumerable<SmsTemplate> GetSmsTemplates(int? templateId)
        {
            const string storedProcedure = "sm_Get_Template";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@TemplateId", templateId);
                    var result = connection.Query<SmsTemplate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return result;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
        #region SMS Template Type
        public async Task<int> UpdateTemplateType(List<SmsTemplateType> sms, long? userId)
        {
            const string storedProcedure = "sm_Update_TempType";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsTemplateType>>(sms));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<int> AddNewTemplateType(List<SmsTemplateType> sms, long? userId)
        {
            const string storedProcedure = "sm_Insert_TempType";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsTemplateType>>(sms));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<int> DeleteTemplateTypes(List<SmsTemplateType> smsTemplateTypes)
        {
            const string storedProcedure = "cus_Del_TemplateTypes";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsTemplateType>>(smsTemplateTypes));
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public IEnumerable<SmsTemplateType> GetSmsTemplateTypes(int? typeId)
        {
            const string storedProcedure = "sm_Get_TempType";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@TypeId", typeId);
                    var result = connection.Query<SmsTemplateType>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return result;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion

        #region Customer
        public ResponseList<IEnumerable<SmsCustomer>> GetCustomers(PagingParams pagingParams, int? categoryId = null, int? cusId = null)
        {
            const string storedProcedure = "cus_Get_Customer";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    if (pagingParams == null)
                    {
                        pagingParams = new PagingParams();
                    }
                
                    param.Add("@Offset", pagingParams.Offset);
                    param.Add("@PageSize", pagingParams.PageSize);
                    param.Add("@Search", pagingParams.Search);

                    param.Add("@SortCol", pagingParams.SortCol);
                    param.Add("@SortDir", pagingParams.SortDir);
                    param.Add("@CategoryId", categoryId);

                    param.Add("@CusId", cusId);
                    param.Add("@Total", 0, DbType.Int32, ParameterDirection.InputOutput);
                    param.Add("@TotalFiltered", 0, DbType.Int32, ParameterDirection.InputOutput);

                    var result = connection.Query<SmsCustomer>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                    return new ResponseList<IEnumerable<SmsCustomer>>(result, param.Get<int>("@Total"), param.Get<int>("@TotalFiltered"));

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<int> UpdateCustomer(List<SmsCustomer> customers, long? userId)
        {
            const string storedProcedure = "cus_Update_Customer";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsCustomer>>(customers));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<int> AddNewCustomer(List<SmsCustomer> customers, long? userId)
        {
            const string storedProcedure = "cus_Insert_Customer";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsCustomer>>(customers));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<int> DeleteCustomers(List<SmsCustomer> customers)
        {
            const string storedProcedure = "cus_Del_Customers";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsCustomer>>(customers));
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
       


        #endregion

        #region Customer Category

        public IEnumerable<SmsCustomerCategory> GetCustomerCategory()
        {
            const string storedProcedure = "cus_Get_CusCategory";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    return connection.Query<SmsCustomerCategory>(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<int> DeleteCustomerCategory(int categoryId, bool? delCustomer)
        {
            const string storedProcedure = "cus_Del_CusCategory";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@CategoryId", categoryId);
                    param.Add("@DelCustomer", delCustomer);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public SmsCustomerCategory GetCustomerCategoryDetail(int categoryId)
        {
            const string storedProcedure = "cus_Get_CusCategoryDetail";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@CategoryId", categoryId);
                    return connection.Query<SmsCustomerCategory>(storedProcedure, param, commandType: CommandType.StoredProcedure).FirstOrDefault();

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        
        public async Task<int> UpdateCustomerCategory(List<SmsCustomerCategory> customers, long? userId)
        {
            const string storedProcedure = "cus_Update_CusCategory";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsCustomerCategory>>(customers));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<int> AddNewCustomerCategory(List<SmsCustomerCategory> customers, long? userId)
        {
            const string storedProcedure = "cus_Insert_CusCategory";
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var param = new DynamicParameters();
                    param.Add("@XML", Common.Utils.SerializeXml<List<SmsCustomerCategory>>(customers));
                    param.Add("@UserID", userId);
                    return await connection.ExecuteAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion

    }


}
