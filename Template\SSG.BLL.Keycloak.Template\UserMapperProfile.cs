﻿using AutoMapper;

// For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860

namespace SSG.BLL.Keycloak.Template
{
    public class EntityModelMapperProfile : Profile
    {
        public EntityModelMapperProfile()
        {
            // CreateMap<SMS, SMSEntity>();
            // CreateMap<SMSEntity, SMS>();
            // CreateMap<SmsModel, CreateSmsModel>();
            // CreateMap<OffDocumentLabelAdd, OffDocumentLabelEntity>();
            // CreateMap<OffDocumentLabelEntity, OffDocumentLabel>();
            // CreateMap<OffApproveAdd, OffApproveEntity>();
            // CreateMap<OffApproveEntity, OffApprove>();
            // CreateMap<OffPermission, OffPermissionEntity>();
            // CreateMap<OffActivityAdd, OffActivityEntity>();
            // CreateMap<OffActivityEntity, OffActivity>();
            // CreateMap<OffTaskAdd, OffTaskEntity>();
            // CreateMap<OffProjectUpdate, OffProjectEntity>();
            // CreateMap<OffProjectAdd, OffProjectEntity>();
            // CreateMap<OffBuildingAdd, OffBuildingEntity>();
            // CreateMap<OffBuildingApproveAdd, OffBuildingApproveEntity>();
            // CreateMap<OffBuildingProgressAdd, OffBuildingProgressEntity>();
            // CreateMap<invTransactionSet, invTransactionSet>().ForAllMembers(opt => opt.Condition((employee, dto, member) => member != null));
            // CreateMap<smtProductStampFromDb, smtProductStamp>()
            //     .ForMember(d => d.ProductName, o => o.MapFrom(s => s.product_name))
            //     .ForMember(d => d.Barcode, o => o.MapFrom(s => s.bar_code))
            //     .ForMember(d => d.Price, o => o.MapFrom(s => s.sell_price))
            //     .ForMember(d => d.PromotionPrice, o => o.MapFrom(s => s.promotion_price))
            //     .ForMember(d => d.Unit, o => o.MapFrom(s => s.unit_name))
            //     .ForMember(d => d.Discount, o => o.MapFrom(s => s.Discount))
            //     // .ForMember(d => d.PromotionPrice, o => o.MapFrom(s => s.promotion_price))
            //     .ForMember(d => d.StartDate, o => o.MapFrom(s => s.start_date))
            //     .ForMember(d => d.EndDate, o => o.MapFrom(s => s.end_date));
        }
    }
}
