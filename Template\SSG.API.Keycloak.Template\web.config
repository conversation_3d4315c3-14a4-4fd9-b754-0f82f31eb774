﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!--
    Configure your application settings in appsettings.json. Learn more at http://go.microsoft.com/fwlink/?LinkId=786380
  -->
  <!--  <configSections>-->
  <!--    ~1~ For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 @1@-->
  <!--    <section name="unity" type="Microsoft.Practices.Unity.Configuration.UnityConfigurationSection, Microsoft.Practices.Unity.Configuration" />-->
  <!--    <section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog" />-->
  <!--  </configSections>-->
  <!--  <unity xmlns="http://schemas.microsoft.com/practices/2010/unity">-->
  <!--    <assembly name="SunshineAPI" />-->
  <!--    <assembly name="Sunshine.DAL" />-->
  <!--    <assembly name="Sunshine.BLL" />-->
  <!--    <container>-->
  <!--      <register type="Sunshine.DAL.Interfaces.IUnitOfWork.ISunshineHousingUnitOfWork" mapTo="Sunshine.DAL.UnitOfWork.SunshineHousingUnitOfWork">-->
  <!--        <lifetime type="singleton" />-->
  <!--        <constructor>-->
  <!--          ~1~Set placeholder for value attribute and replace it at runtime@1@-->
  <!--          <param name="connectionString" value="SunshineHousingSRE" />-->
  <!--        </constructor>-->
  <!--      </register>-->
  <!--      -->
  <!--      ~1~mapping for repository@1@-->
  <!--      <register type="Sunshine.DAL.Interfaces.IRepositories.ISunshineHousingRepository" mapTo="MP.API.DAL.Repositories.SunshineHousingRepository" />-->
  <!--      <register type="Sunshine.BLL.BusinessServiceInterfaces.ISunshineHousingService" mapTo="Sunshine.BLL.BusinessService.SunshineHousingService" />-->
  <!--    </container>-->
  <!--  </unity>-->
  <system.webServer>
    <handlers>
      <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
    </handlers>
    <aspNetCore processPath="%LAUNCHER_PATH%" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" forwardWindowsAuthToken="false" startupTimeLimit="3600" requestTimeout="23:00:00" hostingModel="InProcess">
      <environmentVariables>
        <environmentVariable name="COMPLUS_ForceENC" value="1" />
        <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
      </environmentVariables>
    </aspNetCore>
  </system.webServer>
</configuration>