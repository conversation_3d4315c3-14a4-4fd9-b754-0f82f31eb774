﻿using System.ComponentModel.DataAnnotations;

namespace SSG.API.Keycloak.Template.Authorization
{
    public class Scope
    {
        [Display(Description = "Đọc")]
        public const string Read = "scope:api:read";

        [Display(Description = "Thêm mới")]
        public const string Add = "scope:api:add";

        [Display(Description = "Sửa")]
        public const string Edit = "scope:api:edit";

        [Display(Description = "Xóa")]
        public const string Delete = "scope:api:delete";

        [Display(Description = "Đọc báo cáo số 1")]
        public const string ReadReport1 = "scope:api:report:1";

        [Display(Description = "Đọc báo cáo số 2")]
        public const string ReadReport2 = "scope:api:report:2";

        [Display(Description = "Đọc báo cáo số 3")]
        public const string ReadReport3 = "scope:api:report:3";

        [Display(Description = "Đọc báo cáo số 4")]
        public const string ReadReport4 = "scope:api:report:4";

        [Display(Description = "Đọc báo cáo số 5")]
        public const string ReadReport5 = "scope:api:report:5";

        [Display(Description = "Đọc báo cáo số 6")]
        public const string ReadReport6 = "scope:api:report:6";

        [Display(Description = "Đọc báo cáo số 7")]
        public const string ReadReport7 = "scope:api:report:7";

        [Display(Description = "Đọc báo cáo số 8")]
        public const string ReadReport8 = "scope:api:report:8";

        [Display(Description = "Đọc báo cáo số 9")]
        public const string ReadReport9 = "scope:api:report:9";

        [Display(Description = "Đọc báo cáo số 10")]
        public const string ReadReport10 = "scope:api:report:10";

        [Display(Description = "Đọc báo cáo số 11")]
        public const string ReadReport11 = "scope:api:report:11";

        [Display(Description = "Đọc báo cáo số 12")]
        public const string ReadReport12 = "scope:api:report:12";

        [Display(Description = "Đọc báo cáo số 13")]
        public const string ReadReport13 = "scope:api:report:13";

        [Display(Description = "Đọc báo cáo số 14")]
        public const string ReadReport14 = "scope:api:report:14";
    }
}
