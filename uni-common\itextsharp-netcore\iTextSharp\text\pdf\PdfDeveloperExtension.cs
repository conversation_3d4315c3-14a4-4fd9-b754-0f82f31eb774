/*
    This file is part of the iText (R) project.
    Copyright (c) 1998-2020 iText Group NV
    Authors: <AUTHORS>

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU Affero General Public License version 3
    as published by the Free Software Foundation with the addition of the
    following permission added to Section 15 as permitted in Section 7(a):
    FOR ANY PART OF THE COVERED WORK IN WHICH THE COPYRIGHT IS OWNED BY
    ITEXT GROUP. ITEXT GROUP DISCLAIMS THE WARRANTY OF NON INFRINGEMENT
    OF THIRD PARTY RIGHTS
    
    This program is distributed in the hope that it will be useful, but
    WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY
    or FITNESS FOR A PARTICULAR PURPOSE.
    See the GNU Affero General Public License for more details.
    You should have received a copy of the GNU Affero General Public License
    along with this program; if not, see http://www.gnu.org/licenses or write to
    the Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
    Boston, MA, 02110-1301 USA, or download the license from the following URL:
    http://itextpdf.com/terms-of-use/
    
    The interactive user interfaces in modified source and object code versions
    of this program must display Appropriate Legal Notices, as required under
    Section 5 of the GNU Affero General Public License.
    
    In accordance with Section 7(b) of the GNU Affero General Public License,
    a covered work must retain the producer line in every PDF that is created
    or manipulated using iText.
    
    You can be released from the requirements of the license by purchasing
    a commercial license. Buying such a license is mandatory as soon as you
    develop commercial activities involving the iText software without
    disclosing the source code of your own applications.
    These activities include: offering paid services to customers as an ASP,
    serving PDFs on the fly in a web application, shipping iText with a closed
    source product.
    
    For more information, please contact iText Software Corp. at this
    address: <EMAIL>
 */
using System;

namespace iTextSharp.text.pdf {

    /**
    * Beginning with BaseVersion 1.7, the extensions dictionary lets developers
    * designate that a given document contains extensions to PDF. The presence
    * of the extension dictionary in a document indicates that it may contain
    * developer-specific PDF properties that extend a particular base version
    * of the PDF specification.
    * The extensions dictionary enables developers to identify their own extensions
    * relative to a base version of PDF. Additionally, the convention identifies
    * extension levels relative to that base version. The intent of this dictionary
    * is to enable developers of PDF-producing applications to identify company-specific
    * specifications (such as this one) that PDF-consuming applications use to
    * interpret the extensions.
    * @since   2.1.6
    */
    public class PdfDeveloperExtension {

        /** An instance of this class for Adobe 1.7 Extension level 3. */
        public static readonly PdfDeveloperExtension ADOBE_1_7_EXTENSIONLEVEL3 =
            new PdfDeveloperExtension(PdfName.ADBE, PdfWriter.PDF_VERSION_1_7, 3);
      	/** An instance of this class for ETSI 1.7 Extension level 2. */
	    public static readonly PdfDeveloperExtension ESIC_1_7_EXTENSIONLEVEL2 =
		    new PdfDeveloperExtension(PdfName.ESIC, PdfWriter.PDF_VERSION_1_7, 2);
	    /** An instance of this class for ETSI 1.7 Extension level 5. */
	    public static readonly PdfDeveloperExtension ESIC_1_7_EXTENSIONLEVEL5 =
		    new PdfDeveloperExtension(PdfName.ESIC, PdfWriter.PDF_VERSION_1_7, 5);

        /** The prefix used in the Extensions dictionary added to the Catalog. */
        protected PdfName prefix;
        /** The base version. */
        protected PdfName baseversion;
        /** The extension level within the baseversion. */
        protected int extensionLevel;
        
        /**
        * Creates a PdfDeveloperExtension object.
        * @param prefix    the prefix referring to the developer
        * @param baseversion   the number of the base version
        * @param extensionLevel    the extension level within the baseverion.
        */
        public PdfDeveloperExtension(PdfName prefix, PdfName baseversion, int extensionLevel) {
            this.prefix = prefix;
            this.baseversion = baseversion;
            this.extensionLevel = extensionLevel;
        }

        /**
        * Gets the prefix name.
        * @return  a PdfName
        */
        virtual public PdfName Prefix {
            get {
                return prefix;
            }
        }

        /**
        * Gets the baseversion name.
        * @return  a PdfName
        */
        virtual public PdfName Baseversion {
            get {
                return baseversion;
            }
        }

        /**
        * Gets the extension level within the baseversion.
        * @return  an integer
        */
        virtual public int ExtensionLevel {
            get {
                return extensionLevel;
            }
        }
        
        /**
        * Generations the developer extension dictionary corresponding
        * with the prefix.
        * @return  a PdfDictionary
        */
        virtual public PdfDictionary GetDeveloperExtensions() {
            PdfDictionary developerextensions = new PdfDictionary();
            developerextensions.Put(PdfName.BASEVERSION, baseversion);
            developerextensions.Put(PdfName.EXTENSIONLEVEL, new PdfNumber(extensionLevel));
            return developerextensions;
        }
    }
}
