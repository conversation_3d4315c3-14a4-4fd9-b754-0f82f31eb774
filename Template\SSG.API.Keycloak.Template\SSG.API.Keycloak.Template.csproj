﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>netcoreapp3.1</TargetFramework>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<PreserveCompilationContext>true</PreserveCompilationContext>
		<AssemblyName>SSG.API.Keycloak.Template</AssemblyName>
		<OutputType>Exe</OutputType>
		<PackageId>SSG.API.Yamaha_Training</PackageId>
		<UserSecretsId>6b793af2-3110-4fb8-9d10-6e7aff2e7745</UserSecretsId>
	</PropertyGroup>

	<PropertyGroup Condition=" '$(RunConfiguration)' == 'SSG.API.Yamaha_Training' " />
	<ItemGroup>
		<Compile Remove="Controllers\Version1\ProductBarcodeController.cs" />
		<Compile Remove="Controllers\Version1\SupplierPriceController.cs" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="reports\BZW-DT08-Khoahocthuengoai.xlsx" />
		<None Remove="reports\BZW-DT11-Baocaotonghop.xlsx" />
		<None Remove="reports\BZW-DT13-Chuathamgiakhoadaotao.xlsx" />
		<None Remove="reports\BZW-DT14-DSkhoahochangnamtheochucvu.xlsx" />
		<None Remove="reports\BZW-DT6-Baocaotong.xlsx" />
		<None Remove="reports\BZW-DT6-Baocaotong_.xlsx" />
		<None Remove="reports\cancel_input_order.xlsx" />
		<None Remove="reports\input_order.xlsx" />
		<None Remove="reports\report_EndofDay_Ver.xlsx" />
		<None Remove="reports\report_export_consignment.xlsx" />
		<None Remove="reports\report_export_kitchen.xlsx" />
		<None Remove="reports\report_import_consignment.xlsx" />
		<None Remove="reports\report_khoahoc_bc1.xlsx" />
		<None Remove="reports\report_khoahoc_bc12.xlsx" />
		<None Remove="reports\report_khoahoc_bc2.xlsx" />
		<None Remove="reports\report_khoahoc_bc3.xlsx" />
		<None Remove="reports\report_khoahoc_bc4.xlsx" />
		<None Remove="reports\report_khoahoc_bc5.xlsx" />
		<None Remove="reports\report_khoahoc_bc7.xlsx" />
		<None Remove="reports\report_khoahoc_bc9.xlsx" />
		<None Remove="reports\report_order.xlsx" />
		<None Remove="reports\report_product_cancel.xlsx" />
		<None Remove="reports\report_purchase_back.xlsx" />
	</ItemGroup>

	<ItemGroup>
	  <_WebToolingArtifacts Remove="Properties\PublishProfiles\Yamaha_Test.pubxml" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="reports\BZW-DT11-Baocaotonghop.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\BZW-DT13-Chuathamgiakhoadaotao.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\BZW-DT14-DSkhoahochangnamtheochucvu.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\BZW-DT6-Baocaotong.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\BZW-DT6-Baocaotong_.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\cancel_input_order.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\input_order.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_EndofDay_Ver.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_export_consignment.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_export_kitchen.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_import_consignment.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc9.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc1.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc12.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc2.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc5.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc7.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc3.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_khoahoc_bc4.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_order.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_product_cancel.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\report_purchase_back.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\BZW-DT08-Khoahocthuengoai.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="reports\BZW-DT10-Thongketheothoigian.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<None Update="wwwroot\**\*">
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="10.0.0" />
		<PackageReference Include="EPPlus" Version="5.2.1" />
		<PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.22" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="3.1.6" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1.6" />
		<!--<PackageReference Include="Microsoft.AspNetCore.All" Version="2.0.7" />-->
		<!-- <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Hosting" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Logging" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Logging.Configuration" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="3.1.6" /> -->
		<!-- <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="3.1.6" /> -->
		<PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
		<PackageReference Include="NSwag.AspNetCore" Version="13.6.2" />
		<PackageReference Include="Serilog.AspNetCore" Version="3.4.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.7.1" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Logs\" />
		<Folder Include="wwwroot\stamps\" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\SSG.BLL.Keycloak.Template\SSG.BLL.Keycloak.Template.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="service-account-key-super-app-prod.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>
