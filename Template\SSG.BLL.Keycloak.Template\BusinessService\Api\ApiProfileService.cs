﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces.Api;
using SSG.DAL.Keycloak.Template.Interfaces.Api;
using SSG.Model.KecloakTemplate.Api;
using SSG.Model.KecloakTemplate.Api.Profile;

namespace SSG.BLL.Keycloak.Template.BusinessService.Api
{
    public class ApiProfileService : IApiProfileService
    {
        private readonly ILogger<ApiProfileService> _logger;
        private readonly IApiProfileRepository _repository;

        public ApiProfileService(IApiProfileRepository repository, ILogger<ApiProfileService> logger)
        {
            _logger = logger;
            _repository = repository;
        }

        public async Task<tplApi<tplUserProfile>> GetUserProfile(string accessToken)
        {
            try
            {
                return await _repository.GetUserProfile(accessToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"{ex}");
                throw;
            }
        }
    }
}
