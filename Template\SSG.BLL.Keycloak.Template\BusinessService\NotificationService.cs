﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Amazon.Runtime;
using Amazon.SimpleEmail;
using Amazon.SimpleEmail.Model;
using FluentEmail.Mailgun;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SSG.BLL.BusinessServiceInterfaces;
using SSG.Common;
using SSG.DAL.Interfaces;
using SSG.Model;
using SSG.Model.Api;
using SSG.Model.Email;
using SSG.Model.SMS;
using SSG.Model.SMS.RequestAPI;
using SSG.Model.SMS.ViewModel;
using SSG.Utils;

namespace SSG.BLL.BusinessService
{
    /// <summary>
    /// NotificationService
    /// </summary>
    /// 16/11/2016 1:57 PM
    /// <seealso cref="INotificationService" />
    class NotificationService : INotificationService
    {
        private readonly INotificationRepository _notificationRepository;
        private readonly ISmsSender _smsSender;
        private readonly IEmailSender _emailSender;
        protected readonly ILogger _logger;

        public NotificationService(
            INotificationRepository notificationRepository,
            ISmsSender smsSender,
            IEmailSender emailSender,
            ILoggerFactory logger)
        {
            if (notificationRepository == null)
            {
                throw new Exception("No SQL Connection");
            }
            _notificationRepository = notificationRepository;

            _smsSender = smsSender;
            _emailSender = emailSender;
            _logger = logger.CreateLogger(GetType().Name);
        }

        public Task<int> AddNewSMS(List<SMSEntity> sms, string clientId, string userId)
        {
            return _notificationRepository.AddNewSMS(sms, clientId, userId);
        }

        public ResponseList<IEnumerable<SMS>> GetSMS(PagingParams pagingParams, long? sendId = null, long? id = null)
        {
            return _notificationRepository.GetSMS(pagingParams, sendId, id);
        }

        public Task<int> UpdateSMS(List<SMSEntity> sms, string clientId, string userId)
        {
            return _notificationRepository.UpdateSMS(sms, clientId, userId);
        }

        public Task<long> AddNewSMSGroup(SMSGroup smsGroup, string clientId, string userId)
        {
            return _notificationRepository.AddNewSMSGroup(smsGroup, clientId, userId);
        }

        public ResponseList<IEnumerable<SMSGroup>> GetSMSGroup(PagingParams pagingParams, long? sendId = null)
        {
            return _notificationRepository.GetSMSGroup(pagingParams, sendId);
        }

        public Task<int> UpdateSMSGroup(SMSGroup smsGroup, string clientId, string userId)
        {
            return _notificationRepository.UpdateSMSGroup(smsGroup, clientId, userId);
        }

        public Task<int> UpdateTemplate(List<SmsTemplate> sms, long? userId)
        {
            return _notificationRepository.UpdateTemplate(sms, userId);
        }

        public Task<int> AddNewTemplate(List<SmsTemplate> sms, long? userId)
        {
            return _notificationRepository.AddNewTemplate(sms, userId);
        }

        public IEnumerable<SmsTemplate> GetSmsTemplates(int? templateId)
        {
            return _notificationRepository.GetSmsTemplates(templateId);
        }

        public Task<int> DeleteTemplates(List<SmsTemplate> smsTemplates)
        {
            return _notificationRepository.DeleteTemplates(smsTemplates);
        }

        public Task<int> UpdateTemplateType(List<SmsTemplateType> sms, long? userId)
        {
            return _notificationRepository.UpdateTemplateType(sms, userId);
        }

        public Task<int> AddNewTemplateType(List<SmsTemplateType> sms, long? userId)
        {
            return _notificationRepository.AddNewTemplateType(sms, userId);
        }

        public IEnumerable<SmsTemplateType> GetSmsTemplateTypes(int? typeId)
        {
            return _notificationRepository.GetSmsTemplateTypes(typeId);
        }

        public Task<int> DeleteTemplateTypes(List<SmsTemplateType> smsTemplateTypes)
        {
            return _notificationRepository.DeleteTemplateTypes(smsTemplateTypes);
        }

        public ResponseList<IEnumerable<SmsCustomer>> GetCustomers(PagingParams pagingParams, int? categoryId = null, int? cusId = null)
        {
                return _notificationRepository.GetCustomers(pagingParams, categoryId, cusId);
        }
        
        public Task<int> UpdateCustomer(List<SmsCustomer> customers, long? userId)
        {
            return _notificationRepository.UpdateCustomer(customers, userId);
        }

        public Task<int> AddNewCustomer(List<SmsCustomer> customers, long? userId)
        {
            return _notificationRepository.AddNewCustomer(customers, userId);
        }

        public Task<int> DeleteCustomers(List<SmsCustomer> customers)
        {
            return _notificationRepository.DeleteCustomers(customers);
        }

        public IEnumerable<SmsCustomerCategory> GetCustomerCategory()
        {
            return _notificationRepository.GetCustomerCategory();
        }

        public Task<int> UpdateCustomerCategory(List<SmsCustomerCategory> customers, long? userId)
        {
            return _notificationRepository.UpdateCustomerCategory(customers, userId);
        }

        public Task<int> AddNewCustomerCategory(List<SmsCustomerCategory> customers, long? userId)
        {
            return _notificationRepository.AddNewCustomerCategory(customers, userId);
        }

        public Task<int> DeleteCustomerCategory(int categoryId, bool? delCustomer)
        {
            return _notificationRepository.DeleteCustomerCategory(categoryId, delCustomer);
        }
        public SmsCustomerCategory GetCustomerCategoryDetail(int categoryId)
        {
            return _notificationRepository.GetCustomerCategoryDetail(categoryId);
        }

        public async Task<BaseResponse<List<Item>>> SendSms(CreateSmsModel sms, string clientId, string userId)
        {
            var baseResponse = new BaseResponse<List<Item>>(ApiResult.Success, new List<Item>());
            bool hasFailed = false;

            sms.SendPhone = sms.SendPhone ?? "";
            sms.SendPhone = sms.SendPhone.Trim();
            List<SMSEntity> result = new List<SMSEntity>();
            DateTime sendDate;
            if (sms.SendDate != null)
            {
                sendDate = sms.SendDate.Value;
            }
            else
            {
                DateTime.TryParseExact(Utils.StringHelper.DateToString(DateTime.Now, Constants.SmsDateTimeFormat), Constants.SmsDateTimeFormat, null, DateTimeStyles.None, out sendDate);
            }

            SMSGroup smsGroup = new SMSGroup()
            {
                Content = sms.Content,
                SendDate = sendDate,
                IntSend = (int)SmsStatus.Pending,

                Status = SmsStatus.Pending.GetDescription(),
                Title = sms.Title
            };
            var sendId = await AddNewSMSGroup(smsGroup, clientId, userId);
            _logger.LogInformation("SendSMS: AddNewSMSGroup with sendId: {p1}", sendId);

            //send sms with cateogory
            if (sms.CustomerCategoryId != null && sms.CustomerCategoryId.Any())
            {
                foreach (var categoryId in sms.CustomerCategoryId)
                {
                    if (categoryId > 0)
                    {
                        PagingParamsCustomer pagingParams = new PagingParamsCustomer()
                        {
                            Offset = 0,
                            PageSize = -1 //get all
                        };
                        var listCustomer = GetCustomers(pagingParams, categoryId);
                        if (listCustomer != null && listCustomer.Data.Any())
                        {
                            var phones = listCustomer.Data.Select(c => c.NorPhone1 ?? c.NorPhone2);
                            foreach (var phone in phones)
                            {
                                var smsResponseModel = await _smsSender.SendSmsAsync(phone, sms.Content, sms.SendDate);
                                var smsResult = GetSMSResult(smsResponseModel, phone, sms.Content, sendDate, sendId);
                                result.Add(smsResult);
                                if (smsResult.IntSend == (int) SmsStatus.SentFail)
                                {
                                    hasFailed = true;
                                }
                                baseResponse.Data.Add(new Item()
                                {
                                    ItemName = phone,
                                    Status = smsResult.Status
                                });
                            }
                        }
                    }
                }
            }
            //send sms with direct number
            var listPhoneInline = sms.SendPhone.Split(',');

            foreach (var phone in listPhoneInline)
            {
                var smsResponseModel = await _smsSender.SendSmsAsync(phone, sms.Content, sms.SendDate);
                var smsResult = GetSMSResult(smsResponseModel, phone, sms.Content, sendDate, sendId);
                result.Add(smsResult);
                if (smsResult.IntSend == (int)SmsStatus.SentFail)
                {
                    hasFailed = true;
                }
                baseResponse.Data.Add(new Item()
                {
                    ItemName = phone,
                    Status = smsResult.Status
                });
            }
            await AddNewSMS(result, clientId, userId);
            if (hasFailed)
            {
                baseResponse.SetStatus(ApiResult.Fail);
            }
            return baseResponse;
        }

        public async Task<EmailResponse> SendEmail(CreateEmailModel emailModel, string clientId, string userId)
        {
            var emails = new List<string>();
            //send emailModel with cateogory
            if (emailModel.CustomerCategoryId != null && emailModel.CustomerCategoryId.Any())
            {
                foreach (var categoryId in emailModel.CustomerCategoryId)
                {
                    if (categoryId > 0)
                    {
                        PagingParamsCustomer pagingParams = new PagingParamsCustomer()
                        {
                            Offset = 0,
                            PageSize = -1 //get all
                        };
                        var listCustomer = GetCustomers(pagingParams, categoryId);
                        if (listCustomer != null && listCustomer.Data.Any())
                        {
                            emails.AddRange(listCustomer.Data.Where(c => !string.IsNullOrEmpty(c.Email)).Select(c => c.Email));
                        }
                    }
                }
            }
            //send emailModel with direct number
            var listEmailInline = emailModel.Emails?.Split(',');
            if (listEmailInline != null && listEmailInline.Any())
            {
                emails.AddRange(listEmailInline);
            }

            EmailGroup smsGroup = new EmailGroup()
            {
                Content = emailModel.Content,
                SendDate = emailModel.SendDate,
                Status = (int)SmsStatus.Pending,
                
                SendName = emailModel.SendName,
                SendEmail = emailModel.SendEmail,
                Title = emailModel.Title
            };
            EmailResponse response = null;
            try
            {
                //response = await (Task<SendEmailResponse>) _emailSender.SendAmazonEmail(emails, emailModel.Title, emailModel.Content);
                var directoryPath = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss-fff");

                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }
                await DownloadAttachment(directoryPath, emailModel.Attachs);
                response = await (Task<EmailResponse>) _emailSender.SendMailgunEmail(emails, smsGroup, ListFiles(directoryPath));
                CleanFolder(directoryPath);
            }
            catch (AmazonServiceException e)
            {
                _logger.LogError("SendEmail: exception: {p1}", e);
                smsGroup.Status = (int) e.StatusCode;
            }
            catch (Exception e)
            {
                _logger.LogError("SendEmail: exception: {p1}", e);
                smsGroup.Status = (int)HttpStatusCode.BadRequest;
            }
            if (response != null)
            {
                smsGroup.Status = (int) response.HttpStatusCode;
                smsGroup.MessageId = response.MessageId;
                //smsGroup.RequestId = response.ResponseMetadata?.RequestId;
            }
           
            var sendId = await AddNewEmailGroup(smsGroup, clientId, userId);
             await AddNewEmail(emails, sendId, clientId, userId);
            _logger.LogInformation("SendEmail: with sendId: {p1}", sendId);
            return response;
        }
        
        public Task<int> AddNewEmail(List<string> sms, long sendId, string clientId, string userId)
        {
            return _notificationRepository.AddNewEmail(sms, sendId, clientId, userId);
        }

        public ResponseList<IEnumerable<EmailGroup>> GetEmailGroup(PagingParams pagingParams, long sendId)
        {
            return _notificationRepository.GetEmailGroup(pagingParams, sendId);
        }

        public ResponseList<IEnumerable<EmailEntity>> GetEmail(PagingParams pagingParams, long sendId, long id)
        {
            return _notificationRepository.GetEmail(pagingParams, sendId, id);
        }

        private Task<long> AddNewEmailGroup(EmailGroup smsGroup, string clientId, string userId)
        {
            return _notificationRepository.AddNewEmailGroup(smsGroup, clientId, userId);
        }

        private SMSEntity GetSMSResult(SMSResponseModel responseModel, string phone, string content, DateTime? sendDate, long batchId)
        {
            SMSEntity result = new SMSEntity()
            {
                Content = content,
                SendPhone = phone,
                IntSend = responseModel.RPLY.ERROR == "0" ? (int)SmsStatus.SentSuccess : (int)SmsStatus.SentFail,

                SendDate = sendDate,
                Status = responseModel.RPLY.ERROR_DESC,
                SendId = batchId
            };
            return result;
        }

        private async Task DownloadAttachment(string parentPath, List<string> attachs)
        {
            if (attachs != null)
            {
                foreach (var url in attachs)
                {
                    Uri remoteImgPathUri = new Uri(url);
                    string remoteImgPathWithoutQuery = remoteImgPathUri.AbsolutePath;
                    string fileName = Path.GetFileName(remoteImgPathWithoutQuery);
                    var filepath = $"{parentPath}/{fileName}";

                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    var response = await client.SendAsync(request);
                    var contentStream = await response.Content.ReadAsStreamAsync();
                    FileStream fileStream = new FileStream(filepath, FileMode.Create, FileAccess.Write, FileShare.None, 3145728, true);
                    await contentStream.CopyToAsync(fileStream);

                    fileStream.Dispose();
                    contentStream.Dispose();
                    response.Dispose();
                    request.Dispose();
                    client.Dispose();
                }
            }
        }
        private void CleanFolder(string folderPath)
        {
            DirectoryInfo di = new DirectoryInfo(folderPath);

            foreach (FileInfo file in di.GetFiles())
            {
                file.Delete();
            }
            foreach (DirectoryInfo dir in di.GetDirectories())
            {
                dir.Delete(true);
            }
            di.Delete();
        }
        private List<string> ListFiles(string folderPath)
        {
            List<string> result = new List<string>();
            DirectoryInfo di = new DirectoryInfo(folderPath);
            foreach (FileInfo file in di.GetFiles())
            {
                result.Add(file.FullName);
            }
            return result;
        }
    }
}
