﻿using Dapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SSG.DAL.Keycloak.Template.Interfaces;
using SSG.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace SSG.DAL.Keycloak.Template.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly ILogger<UserRepository> _logger;
        private readonly string _connectionString;

        public UserRepository(IConfiguration configuration, ILogger<UserRepository> logger)
        {
            this._logger = logger;
            _connectionString = configuration.GetConnectionString("Yamaha_Training");
        }

        //public async Task<YmUserDataPage> GetDataPageAsync(YmUserDataFilter query)
        //{
        //    const string storedProcedure = "sp_YAM_User_Data_Page";
        //    using (var connection = new SqlConnection(_connectionString))
        //    {
        //        await connection.OpenAsync();
        //        var param = new DynamicParameters();
        //        param.Add("@userId", query.userId);
        //        param.Add("@filter", query.filter);
        //        param.Add("@Offset", query.offSet);
        //        param.Add("@PageSize", query.pageSize);
        //        param.Add("@Total", 0, DbType.Int64, ParameterDirection.InputOutput);
        //        param.Add("@TotalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);
        //        var dataMulti = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

        //        var data = new YmUserDataPage();

        //        if (query.offSet == null || query.offSet == 0)
        //        {
        //            data.gridflexs = dataMulti.Read<viewGridFlex>().ToList();
        //        }

        //        var dataList = dataMulti.Read<object>().ToList();
        //        data.dataList = new ResponseList<List<object>>(dataList, param.Get<long>("@Total"), param.Get<long>("@TotalFiltered"));

        //        return data;
        //    }
        //}

        //public async Task<BaseValidate> SetUserDataAsync(string userId, YmUserData data)
        //{
        //    const string storedProcedure = "sp_YAM_User_Data_set";
        //    using (var connection = new SqlConnection(_connectionString))
        //    {
        //        connection.Open();
        //        var param = new DynamicParameters();
        //        param.Add("@userId", userId);
        //        param.Add("@userSet", data.UserId);
        //        param.Add(nameof(data.OrganizerId), data.OrganizerId);
        //        param.Add(nameof(data.Active), data.Active);
        //        var result = await connection.QueryFirstOrDefaultAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //        return result;
        //    }
        //}

        //public async Task<bool> SetUserAsync(YmUserLogin userLogin)
        //{
        //    try
        //    {
        //        const string storedProcedure = "sp_YAM_User_Async";
        //        using (var connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var result = await connection.ExecuteScalarAsync<long>(storedProcedure, userLogin, commandType: CommandType.StoredProcedure);
        //            return result > 0;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex}");
        //        throw;
        //    }
        //}
    }
}
