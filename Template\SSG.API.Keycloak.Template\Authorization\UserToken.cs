﻿using System.Collections.Generic;
using SSG.Model.KecloakTemplate.Api.Profile;

namespace SSG.API.Keycloak.Template.Authorization
{
    public class UserToken
    {
        public string UserId { get; set; }
        public string UserName { get; set; }
        public IEnumerable<string> Roles { get; set; }
        public List<Permission> Permissions { get; set; }
        public tplUserProfile Profile { get; set; }
    }
}
