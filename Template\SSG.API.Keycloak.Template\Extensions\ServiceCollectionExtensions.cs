﻿using System.Net.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RestSharp;
using SSG.BLL.Keycloak.Template.BusinessService;
using SSG.BLL.Keycloak.Template.BusinessService.Api;
using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces;
using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces.Api;
using SSG.DAL.Keycloak.Template.Interfaces;
using SSG.DAL.Keycloak.Template.Interfaces.Api;
using SSG.DAL.Keycloak.Template.Repositories;
using SSG.DAL.Keycloak.Template.Repositories.Api;

namespace SSG.API.Keycloak.Template.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterServices(this IServiceCollection services, IConfiguration configuration)
        {
            // and a lot more Services
            services.AddSingleton(configuration);

            #region core-repo
            services.AddScoped<IClassRepository, ClassRepository>();
            services.AddScoped<IUserRepository,UserRepository>();
            services.AddScoped<IApiProfileRepository, ApiProfileRepository>();
            #endregion

            #region core-service
            services.AddScoped<IClassService, ClassService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IApiProfileService, ApiProfileService>();
            #endregion

            services.AddTransient<HttpClient>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddSingleton(typeof(RestClient), new RestClient { Timeout = 60 * 1000 });
            return services;
        }
    }
}
