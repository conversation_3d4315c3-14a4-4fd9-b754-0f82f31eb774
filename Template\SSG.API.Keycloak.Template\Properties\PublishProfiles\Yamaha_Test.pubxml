<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://apiyamaha-dev.sunshinetech.com.vn/swagger/</SiteUrlToLaunchAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <ProjectGuid>348e3cfb-0fdd-497b-a439-60d8aceed41b</ProjectGuid>
    <SelfContained>false</SelfContained>
    <MSDeployServiceURL>*********</MSDeployServiceURL>
    <DeployIisAppPath>SsgYamahaTraningTest</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <EnableMsDeployAppOffline>True</EnableMsDeployAppOffline>
    <UserName>administrator</UserName>
    <_SavePWD>False</_SavePWD>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>
</Project>