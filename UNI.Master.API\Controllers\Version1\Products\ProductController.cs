﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1.Products
{

    /// <summary>
    /// Sản phẩm
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 07/04/2020 9:31 AM
    /// <seealso cref="ProductController" />
    [Route("api/v1/product/[action]")]
    [Authorize]
    public class ProductController : UniController
    {

        private readonly IProductService _productService;
        private readonly ISysManageService _systemService;


        /// <summary>
        /// Web Controller
        /// </summary>
        /// <param name="productService"></param>
        /// <param name="systemService"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public ProductController(
            IProductService productService,
            ISysManageService systemService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _productService = productService;
            _systemService = systemService;
        }
        /// <summary>
        /// GetProductFilter - Filter
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetProductFilter()
        {
            var result = await _systemService.GetManagerFilter(UserId, "mas_product_filter");
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetProductsPage - Danh sách sản phẩm
        /// </summary>
        /// <param name="prod_line_id"></param>
        /// <param name="customer_Id"></param>
        /// <param name="filter"></param>
        /// <param name="gridWith"></param>
        /// <param name="offSet"></param>
        /// <param name="flt"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetProductsPage(
            [FromQuery] FilterProduct flt)
        {
            try
            {
                flt.ucInput(UserId, ClientId, AcceptLanguage);
                //var flt = new FilterProduct(this.ClientId, this.UserId, offSet, pageSize, filter, gridWith, prod_line_id, customer_Id);
                var rs = await _productService.GetProductsPage(flt);
                var rp = GetResponse(ApiResult.Success, rs);
                return rp;
            }
            catch (Exception e)
            {
                _logger.LogError($"{e}");
                var rp = new BaseResponse<CommonListPage>(ApiResult.Error, e.Message);
                return rp;
            }
        }
        /// <summary>
        /// GetProductInfo - Lấy thông tin sản phẩm
        /// </summary>
        /// <param name="id"></param>
        /// <param name="prod_line_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<productInfo>> GetProductInfo([FromQuery] Guid? id, [FromQuery] Guid? prod_line_id)
        {
            var result = await _productService.GetProductInfo(UserId, id, prod_line_id);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetProductDraft
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<productInfo>> SetProductDraft([FromBody] productInfo prod)
        {
            var result = await _productService.SetProductDraft(UserId, prod);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetProductInfo - Thêm/ sửa sản phẩm
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetProductInfo([FromBody] productInfo prod)
        {
            var result = await _productService.SetProductInfo(UserId, prod);
            if (result.valid)
                return GetResponse(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// DelProductInfo - Xóa sản phẩm
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelProductInfo([FromQuery] Guid id)
        {
            var result = await _productService.DelProductInfo(UserId, id);
            if (result.valid)
                return GetResponse(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public BaseResponse<List<CommonValue>> GetProductCategory([FromQuery] string filter)
        {
            var result = _systemService.GetCommonList(UserId, "products", "prod_name");
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetProductByPackageId - Danh sách sản phẩm áp dụng gói sản phẩm
        /// </summary>
        /// <param name="package_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetProductByPackageId([FromQuery] Guid? package_id)
        {
            try
            {
                var rs = await _productService.GetProductByPackageId(package_id);
                var rp = GetResponse(ApiResult.Success, rs);
                return rp;
            }
            catch (Exception e)
            {
                _logger.LogError($"{e}");
                var rp = new BaseResponse<CommonListPage>(ApiResult.Error, e.Message);
                return rp;
            }
        }
        //#region product variation
        //[HttpGet]
        //public async Task<BaseResponse<CommonViewInfo>> GetProductVariationInfo([FromQuery] Guid? id)
        //{
        //    var result = await _productService.GetProductVariationAsync(id);
        //    return GetResponse(ApiResult.Success, result);
        //}
        //[HttpPost]
        //public async Task<BaseResponse<string>> SetProductVariation([FromBody] CommonViewInfo prod)
        //{
        //    var result = await _productService.SetProductVariationAsync(prod);
        //    if (result.valid)
        //        return GetResponse(ApiResult.Success, result.messages);
        //    else
        //    {
        //        var response = GetResponse<string>(ApiResult.Error, null);
        //        response.SetStatus(2, result.messages);
        //        return response;
        //    }
        //}
        //#endregion

    }
}

