﻿/// <reference path="oidc-client.js" />

var isDevelopment = location.host.indexOf("localhost") >= 0;
var config = {
    authority: "https://auth-yamaha-dev.sunshineapp.vn/auth/realms/Bizzone",
    client_id: "yamaha-training-backend-dev",
    redirect_uri: `${location.origin}/swagger/callback.html`,
    response_type: "id_token token",
    scope: "openid profile email",
    post_logout_redirect_uri: `${location.origin}/swagger`,
    post_token_url: "https://auth-yamaha-dev.sunshineapp.vn/auth/realms/bizzone/protocol/openid-connect/token"
};

if (isDevelopment) {
    config = {
        authority: "https://dev-umee-auth.ssf.vn/auth/realms/umee-backend",
        client_id: "umee-backend-client",
        redirect_uri: "http://localhost:3101/swagger/callback.html",
        response_type: "id_token token",
        scope: "openid profile email",
        post_logout_redirect_uri: "http://localhost:3101/swagger",
        post_token_url: "https://dev-umee-auth.ssf.vn/auth/realms/umee-backend/protocol/openid-connect/token"
    };
}

var myUser;
var mgr = new Oidc.UserManager(config);

function login() {
    mgr.signinRedirect();
}

function logout() {
    mgr.signoutRedirect();
}

function onClickAuthBtn() {
    if (!myUser) {
        login();
    } else {
        logout();
    }
}

function getMyUser() {
    mgr.getUser().then(function (user) {
        if (user) {
            myUser = user;
            window.authorizations.add("key", new SwaggerClient.ApiKeyAuthorization("Authorization", "Bearer " + user.access_token, "header"));
            $("#authen").html("Logout");

            /*
            var xhr = new XMLHttpRequest();
            xhr.open("POST", config.post_token_url, true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhr.setRequestHeader("Authorization", `Bearer ${user.access_token}`);
            xhr.onreadystatechange = function () {
                if (this.readyState === XMLHttpRequest.DONE && this.status === 200) {
                    var result = JSON.parse(xhr.responseText);
                    window.authorizations.add("key",
                        new SwaggerClient.ApiKeyAuthorization("Authorization",
                            `Bearer ${result.access_token}`,
                            "header"));
                }

                if (this.readyState === XMLHttpRequest.DONE && this.status === 401) {
                    logout();
                }
            }
            xhr.send(`grant_type=urn:ietf:params:oauth:grant-type:uma-ticket&audience=${config.client_id}`);
            */
        }
        else {
            $("#authen").html("Login");
        }
    });
}

$(document).ready(function () {
    $("#authen").on("click", onClickAuthBtn);
    getMyUser();
});