﻿using SSG.Model;
using System;
using System.Threading.Tasks;

namespace SSG.BLL.Keycloak.Template.BusinessServiceInterfaces
{
    public interface IClassService
    {
        //Task<YmClassPage> GetPageAsync(FilterBase query);
        //Task<YmClassInfo> GetInfoAsync(string userId, Guid? id);
        //Task<BaseValidate> SetInfoAsync(string userId, YmClassInfo info);
        Task<BaseValidate> DeleteAsync(string userId, Guid? id);
        //Task<BaseResponse<ImportClassResponse>> Import(string userId, string xml);
    }
}
