# syntax=docker/dockerfile:1
FROM mcr.microsoft.com/dotnet/core/sdk:3.1  AS build-env
WORKDIR /app

COPY . .

WORKDIR /app/sunshine-common/SSG.Utils
RUN dotnet restore 

WORKDIR /app/UME.DAL.Config
RUN dotnet restore 

WORKDIR /app/UME.API.Config
RUN dotnet restore 

RUN dotnet publish -c Release --no-restore -o out

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:3.1
ENV TZ="Asia/Ho_Chi_Minh"
WORKDIR /app
#COPY --from=build-env /app/UmeBLL/Template ./Template
COPY --from=build-env /app/sunshine-common/SSG.Common/Assets ./assets
COPY --from=build-env /app/UME.API.Config/out .
# COPY --from=build-env /app/appsettings.Development.json .

EXPOSE 8080
ENTRYPOINT ["dotnet", "UME.API.Config.dll"]

# docker build --no-cache -t ume.api.config -f dockers/Dockerfile .
# docker run -it --rm -e ASPNETCORE_ENVIRONMENT='Development' --mount type=bind,source="$(pwd)/appsettings.Development.json,target=/app/appsettings.Development.json" -p 8080:8080 --name myapp ume.api.config
# https://anthonyliriano.com/2021/04/10/21-kubernetes-configmap-and-.net5-appsettings.html