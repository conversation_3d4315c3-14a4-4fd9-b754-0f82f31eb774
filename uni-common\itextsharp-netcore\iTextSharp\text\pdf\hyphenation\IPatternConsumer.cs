using System;
using System.Collections.Generic;
/*
 * Copyright 1999-2004 The Apache Software Foundation.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace iTextSharp.text.pdf.hyphenation {
    /**
    * This interface is used to connect the XML pattern file parser to
    * the hyphenation tree.
    *
    * <AUTHOR> <<EMAIL>>
    */
    public interface IPatternConsumer {

        /**
        * Add a character class.
        * A character class defines characters that are considered
        * equivalent for the purpose of hyphenation (e.g. "aA"). It
        * usually means to ignore case.
        * @param chargroup character group
        */
        void AddClass(String chargroup);

        /**
        * Add a hyphenation exception. An exception replaces the
        * result obtained by the algorithm for cases for which this
        * fails or the user wants to provide his own hyphenation.
        * A hyphenatedword is a vector of alternating String's and
        * {@link Hyphen Hyphen} instances
        */
        void AddException(String word, List<object> hyphenatedword);

        /**
        * Add hyphenation patterns.
        * @param pattern the pattern
        * @param values interletter values expressed as a string of
        * digit characters.
        */
        void AddPattern(String pattern, String values);
    }
}
