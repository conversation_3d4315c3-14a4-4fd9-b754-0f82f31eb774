﻿using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces;
using SSG.DAL.Keycloak.Template.Interfaces;
using SSG.Model;
using System;
using System.Threading.Tasks;

namespace SSG.BLL.Keycloak.Template.BusinessService
{
    public class ClassService : IClassService
    {
        private readonly IClassRepository _repository;

        public ClassService(IClassRepository repository)
        {
            _repository = repository;
        }

        //public async Task<YmClassPage> GetPageAsync(FilterBase query)
        //{
        //    return await _repository.GetPageAsync(query);
        //}

        //public async Task<YmClassInfo> GetInfoAsync(string userId, Guid? id)
        //{
        //    return await _repository.GetInfoAsync(userId, id);
        //}

        //public async Task<BaseValidate> SetInfoAsync(string userId, YmClassInfo info)
        //{
        //    return await _repository.SetInfoAsync(userId, info);
        //}

        public async Task<BaseValidate> DeleteAsync(string userId, Guid? id)
        {
            return await _repository.DeleteAsync(userId, id);
        }
        //public async Task<BaseResponse<ImportClassResponse>> Import(string userId, string xml)
        //{
        //    return await _repository.Import(userId, xml);
        //}
    }
}
