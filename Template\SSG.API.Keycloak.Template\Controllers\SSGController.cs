﻿using System;
using System.Linq;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SSG.Model;
using SSG.Model.Api;

namespace SSG.API.Keycloak.Template.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    /// <author>taint</author>
    /// <createdDate>2/2/2016</createdDate>
    /// <seealso>
    ///     <cref>System.Web.Http.ApiController</cref>
    /// </seealso>
    public class SSGController : ControllerBase
    {
        /// <summary>
        /// logger
        /// </summary>
        protected readonly ILogger _logger;
        /// <summary>
        /// AppSettings
        /// </summary>
        protected readonly AppSettings _appSettings;
       /// <summary>
       /// Contructor
       /// </summary>
       /// <param name="appSettings"></param>
       /// <param name="logger"></param>
        public SSGController(IOptions<AppSettings> appSettings, ILoggerFactory logger)
        {
            _appSettings = appSettings.Value;
            _logger = logger.CreateLogger(GetType().Name);
        }

        /// <summary>
        /// Contructor
        /// </summary>
        /// <param name="logger"></param>
        public SSGController(ILoggerFactory logger)
        {
            _logger = logger.CreateLogger(GetType().Name);
        }

        /// <summary>
        /// Get Errors
        /// </summary>
        protected string Errors
        {
            get
            {
                try
                {
                    var sb = new StringBuilder();

                    foreach (var key in ModelState.Keys)
                    {
                        foreach (var error in ModelState[key].Errors)
                        {
                            sb.Append("Key: " + key + " - " + "Error: " + error.ErrorMessage + " @ " + error.Exception + "<br/>");
                        }
                    }

                    return sb.ToString();
                }
                catch (Exception e)
                {
                    _logger.LogError(e.StackTrace);
                    throw;
                }
            }
        }
        /// <summary>
        /// Control Client
        /// </summary>
        public BaseCtrlClient CtrlClient
        {
            get
            {
                return new BaseCtrlClient
                {
                    ClientId = User.Claims.Where(c => c.Type == "client_id").Select(c1 => c1.Value).FirstOrDefault(),
                    ClientIp = Request.HttpContext.Connection.RemoteIpAddress.ToString(),
                    hostUrl = HttpContext.Request.Scheme + "://" + HttpContext.Request.Host.Value,
                    UserId = User.Claims.Where(c => c.Type == "sub").Select(c1 => c1.Value).FirstOrDefault()
                };
            }
        }
        /// <summary>
        /// GetClaim
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        protected string GetClaim(string key)
        {
            var principal = HttpContext.User.Claims;
            return principal.Single(c => c.Type == key).Value;
        }
        /// <summary>
        /// GetResponse
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="status"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected static BaseResponse<T> GetResponse<T>(ApiResult status, T data)
        {
            return new BaseResponse<T>(status, data);
        }
        /// <summary>
        /// Get Response
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="status"></param>
        /// <param name="data"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        protected static BaseResponse<T> GetResponse<T>(ApiResult status, T data, string message)
        {
            var response = new BaseResponse<T>(status, data);
            response.SetStatus(status, message);
            return response;
        }
    }
}