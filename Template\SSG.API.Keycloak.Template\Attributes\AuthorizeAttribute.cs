﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces;
using System;
using System.Linq;
using System.Reflection;

namespace SSG.API.Keycloak.Template.Attributes
{
    /// <summary>
    /// 
    /// </summary>
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class AuthorizeAttribute : ActionFilterAttribute
    {
        private readonly ILogger<AuthorizeAttribute> _logger;
        private readonly IUserService _userService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="logger"></param>
        public AuthorizeAttribute(IUserService userService, ILogger<AuthorizeAttribute> logger)
        {
            _logger = logger;
            _userService = userService;
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="filterContext"></param>
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var controller = (ControllerBase)filterContext.Controller;
            var isAuthorizeController = controller?.GetType().GetCustomAttributes<Microsoft.AspNetCore.Authorization.AuthorizeAttribute>().FirstOrDefault() != null;
            var isAuthorizeAction = (filterContext.ActionDescriptor as ControllerActionDescriptor)?.MethodInfo.GetCustomAttributes<Microsoft.AspNetCore.Authorization.AuthorizeAttribute>().FirstOrDefault() != null;
            var isAnonymousAction = (filterContext.ActionDescriptor as ControllerActionDescriptor)?.MethodInfo.GetCustomAttributes<AllowAnonymousAttribute>().FirstOrDefault() != null;

            if ((isAuthorizeController && !isAnonymousAction || isAuthorizeAction) && controller?.User?.Identity != null && controller.User.Identity.IsAuthenticated)
            {
                // Async User
                //var result = _userService.SetUserAsync(new YmUserLogin
                //    {
                //        UserId = Guid.Parse(controller.User.GetUserId()),
                //        UserName = controller.User.GetUserName()
                //    })
                //    .GetAwaiter()
                //    .GetResult();

                //if (!result)
                //{
                //    _logger.LogError("Error async user");
                //    filterContext.Result = new StatusCodeResult(StatusCodes.Status500InternalServerError);
                //}

                // Somethings
            }
        }
    }
}
