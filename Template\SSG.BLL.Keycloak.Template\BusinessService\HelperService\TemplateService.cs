﻿using Google.Apis.Auth.OAuth2;
using Google.Apis.Docs.v1;
using Google.Apis.Docs.v1.Data;
using Google.Apis.Drive.v3;
using Google.Apis.Services;
using Newtonsoft.Json;
using SSG.Model.SHome;
using SSG.Utils;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SSG.BLL.Keycloak.Template.BusinessService.HelperService
{
    public class InvTemplateService
    {
        static string _ApplicationName = "ssg-api";
        static string documentWriterCred = "sunshine-super-app-1032f6b27d75-document-writer.json";
        static string[] ScopesDrive = { DriveService.Scope.Drive };
        static string[] ScopesDocs = { DocsService.Scope.Documents };


        #region Get Service
        /// <summary>
        /// Get Google Drive Service
        /// </summary>
        /// <returns></returns>
        public static DriveService GetService3()
        {
            try
            {
                string credentialPath = Path.GetFullPath(documentWriterCred);
                //var json = System.IO.File.ReadAllText(credentialPath);
                //var cr = JsonConvert.DeserializeObject<GoogleServiceAccountCred>(json);
                string[] Scopes = { DriveService.Scope.Drive, DocsService.Scope.Documents };

                ServiceAccountCredential credentials = GoogleCredential.FromFile(credentialPath).CreateScoped(Scopes).UnderlyingCredential as ServiceAccountCredential;

                if (credentials.RequestAccessTokenAsync(CancellationToken.None).Result)
                {
                    //Console.WriteLine("access token: " + credentials.Token.AccessToken);
                    DriveService service = new DriveService(
                    new BaseClientService.Initializer()
                    {
                        HttpClientInitializer = credentials,
                    }
                );
                    return service;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return null;
        }
        /// <summary>
        /// Get Google Docs Service
        /// </summary>
        /// <returns></returns>
        public static DocsService GetDocsService()
        {
            //check  the file exits
            if (!System.IO.File.Exists(Path.GetFullPath(documentWriterCred)))
            {
                return null;
            }
            try
            {
                string credentialPath = Path.GetFullPath(documentWriterCred);
                var json = System.IO.File.ReadAllText(credentialPath);
                var cr = JsonConvert.DeserializeObject<GoogleServiceAccountCred>(json);

                ServiceAccountCredential credential;
                string[] Scopes = ScopesDocs;
                string serviceAccountEmail = cr.client_email;
                string jsonfile = documentWriterCred;
                using (Stream stream = new FileStream(@jsonfile, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    credential = (ServiceAccountCredential)
                        GoogleCredential.FromStream(stream).UnderlyingCredential;

                    var initializer = new ServiceAccountCredential.Initializer(credential.Id)
                    {
                        User = serviceAccountEmail,
                        Key = credential.Key,
                        Scopes = Scopes
                    };
                    credential = new ServiceAccountCredential(initializer);

                    var service = new DocsService(new BaseClientService.Initializer()
                    {
                        HttpClientInitializer = credential,
                        ApplicationName = _ApplicationName,
                    });
                    return service;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return null;
            }
        }
        /// <summary>
        /// Copy file mẫu sang thư mục Output
        /// </summary>
        /// <param name="driveService"></param>
        /// <param name="templateFileId"></param>
        /// <param name="outputFolderId"></param>
        /// <param name="newFileName"></param>
        /// <returns></returns>
        public static async Task<Google.Apis.Drive.v3.Data.File> MoveFiles(DriveService driveService, string templateFileId, string outputFolderId, string newFileName)
        {
            Google.Apis.Drive.v3.Data.File copiedFile = new Google.Apis.Drive.v3.Data.File();
            copiedFile.Name = newFileName;

            //Clone file template
            FilesResource.CopyRequest copyRequest = driveService.Files.Copy(copiedFile, templateFileId);
            copyRequest.SupportsAllDrives = true;
            copyRequest.SupportsTeamDrives = true;
            copiedFile = await copyRequest.ExecuteAsync();

            // Retrieve the existing parents to remove
            FilesResource.GetRequest getRequest = driveService.Files.Get(copiedFile.Id);
            getRequest.Fields = "parents";
            getRequest.SupportsAllDrives = true;
            getRequest.SupportsTeamDrives = true;
            Google.Apis.Drive.v3.Data.File file = await getRequest.ExecuteAsync();
            string previousParents = String.Join(",", file.Parents);

            //Move file to output folder
            FilesResource.UpdateRequest updateRequest = driveService.Files.Update(new Google.Apis.Drive.v3.Data.File(), copiedFile.Id);
            updateRequest.Fields = "id, parents";
            updateRequest.SupportsAllDrives = true;
            updateRequest.SupportsTeamDrives = true;
            updateRequest.AddParents = outputFolderId;
            updateRequest.RemoveParents = previousParents;

            file = await updateRequest.ExecuteAsync();
            if (file != null)
            {
                return file;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Request2ReplaceGDocs
        /// </summary>
        /// <param name="oldString"></param>
        /// <param name="newString"></param>
        /// <param name="requestsList"></param>
        public static void RequestGDocs(List<Request> requestsList, string oldString, string newString)
        {
            Request request = new Request();
            request.ReplaceAllText = new ReplaceAllTextRequest();
            request.ReplaceAllText.ContainsText = new SubstringMatchCriteria();
            request.ReplaceAllText.ContainsText.MatchCase = true;
            request.ReplaceAllText.ContainsText.Text = oldString;
            request.ReplaceAllText.ReplaceText = newString;
            requestsList.Add(request);
        }

        /// <summary>
        /// GetTemplateFolder
        /// </summary>
        /// <param name="service"></param>
        /// <param name="parentfolderId"></param>
        /// <param name="countryCd"></param>
        /// <returns></returns>
        public static string GetTemplateFolder(DriveService service, string parentfolderId, string countryCd)
        {
            try
            {

                Google.Apis.Drive.v3.FilesResource.ListRequest listRequest = service.Files.List();
                listRequest.IncludeItemsFromAllDrives = true;
                listRequest.IncludeTeamDriveItems = true;
                listRequest.SupportsAllDrives = true;
                listRequest.SupportsTeamDrives = true;
                listRequest.Spaces = "drive";
                listRequest.Fields = "nextPageToken, files(id, name, parents, mimeType, modifiedTime)";
                listRequest.Q = "'" + parentfolderId + "' in parents"; // and name = '" + templateFileName +"'";
                // List files.
                IList<Google.Apis.Drive.v3.Data.File> files = listRequest.Execute().Files;
                string folderTemplateId = string.Empty;
                if (files.Count > 0)
                {
                    foreach (var file in files)
                    {
                        if (countryCd != "VN")
                        {
                            return folderTemplateId = file.Id;
                        }
                        else if (file.Name == "Vietnamese" && countryCd == "VN")
                        {
                            return folderTemplateId = file.Id;
                        }
                    }
                }
                return folderTemplateId;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return null;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        public static string GetTemplateFileId(DriveService driveService, string templateFolderId, string templateFileName, string countryCd)
        {
            try
            {
                countryCd = string.IsNullOrEmpty(countryCd) ? "VN" : countryCd;
                //string folderTemplateId = GetTemplateFolder(driveService, parentfolderTemplateId, countryCd);
                Google.Apis.Drive.v3.FilesResource.ListRequest listRequest = driveService.Files.List();
                listRequest.IncludeItemsFromAllDrives = true;
                listRequest.IncludeTeamDriveItems = true;
                listRequest.SupportsAllDrives = true;
                listRequest.SupportsTeamDrives = true;
                listRequest.Spaces = "drive";
                listRequest.Fields = "nextPageToken, files(id, name, parents, mimeType, modifiedTime)";
                listRequest.Q = "'" + templateFolderId + "' in parents and name = '" + templateFileName + "'";
                // List files.
                IList<Google.Apis.Drive.v3.Data.File> files = listRequest.Execute().Files;
                string fileTemplateId = string.Empty;
                if (files.Count > 0)
                {
                    foreach (var file in files)
                    {
                        return fileTemplateId = (file.Name == templateFileName) ? file.Id : file.Id;
                    }
                }
                return fileTemplateId;
            }
            catch (Exception Ex)
            {
                throw new Exception("Request Files.List failed.", Ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="driveService"></param>
        /// <param name="resultLink"></param>
        /// <param name="documentId"></param>
        /// <param name="viewerUrl"></param>
        /// <returns></returns>
        public static async Task<GoogleResultLink> GetGoogleResultLink(DriveService driveService, GoogleResultLink resultLink, string documentId, string viewerUrl)
        {
            try
            {
                //share result file
                Google.Apis.Drive.v3.Data.Permission filePermission = new Google.Apis.Drive.v3.Data.Permission();
                filePermission.Role = "writer";
                filePermission.Type = "anyone";
                Google.Apis.Drive.v3.PermissionsResource.CreateRequest permissionRequest = driveService.Permissions.Create(filePermission, documentId);
                permissionRequest.SupportsAllDrives = true;
                permissionRequest.SupportsTeamDrives = true;
                await permissionRequest.ExecuteAsync();

                FilesResource.GetRequest getRequest = driveService.Files.Get(documentId);
                getRequest.SupportsAllDrives = true;
                getRequest.SupportsTeamDrives = true;
                getRequest.Fields = "id, name, webViewLink, webContentLink";
                Google.Apis.Drive.v3.Data.File file = await getRequest.ExecuteAsync();

                resultLink.WebViewLink = string.Concat(viewerUrl, "/reporting_word_viewer/?fileId=", documentId);
                resultLink.PDFDownloadLink = file.WebViewLink.Replace("/edit?usp=drivesdk", "/export?format=pdf");
                resultLink.MSWordDownloadLink = file.WebViewLink.Replace("/edit?usp=drivesdk", "/export?format=doc");
                return resultLink;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return resultLink;
        }
        #endregion

        /// <summary>
        /// Main method to process
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="iocItem"></param>
        /// <returns></returns>
        //public static async Task<invOrderContract> GenerateContractFile(invOrderContract iocItem, List<Model.Core.coreObject> configs)
        //{
        //    string templateFolderId = configs.Where(c => c.objCode == "TemplateFolder").FirstOrDefault().objValue;
        //    string outputFolderId = configs.Where(c => c.objCode == "OutputFolder").FirstOrDefault().objValue;
        //    string templateID = configs.Where(c => c.objCode == "TemplateId").FirstOrDefault().objValue;
        //    string templateFileName = configs.Where(c => c.objCode == "TemplateName").FirstOrDefault().objValue;

        //    string newFileName = string.Concat(templateFileName, " ", iocItem.ord_id);
        //    string viewerUrl = "https://asia-northeast1-sunshine-app-production.cloudfunctions.net";
        //    //var templateFileName = templateFileObj.Name;
        //    //string templateFolderId = folderTemplateObj.Id;
        //    //string outputFolderId = folderOutputObj.Id;

        //    //Get service
        //    DriveService driveService = GetService3();
        //    DocsService docsService = GetDocsService();

        //    //GoogleResultLink
        //    GoogleResultLink resultLink = new GoogleResultLink();

        //    //Get template file from Google Drive
        //    string templateFileId = GetTemplateFileId(driveService, templateFolderId, templateFileName, "VN");
        //    //Chuyển file mẫu sang thư mục Output
        //    if (!string.IsNullOrEmpty(templateFileId))
        //    {
        //        Google.Apis.Drive.v3.Data.File resultMoveFile = await MoveFiles(driveService, templateFileId, outputFolderId, newFileName);

        //        //Replace file mẫu trong thư mục Output
        //        string documentId = await ReplaceTextGoogleDocs(docsService, iocItem, resultMoveFile);

        //        //Return download link and WebViewLink
        //        resultLink = await GetGoogleResultLink(driveService, resultLink, documentId, viewerUrl);
        //    }
        //    iocItem.ioc_temp_download_url = resultLink.MSWordDownloadLink;
        //    iocItem.ioc_temp_view_url = resultLink.WebViewLink;
        //    return iocItem;
        //}

        ///// <summary>
        ///// ReplaceTextGoogleDocs for Order Report
        ///// </summary>
        ///// <param name="docsService"></param>
        ///// <param name="orderItem"></param>
        ///// <param name="mergedFile"></param>
        ///// <param name="custRelationObj"></param>
        ///// <param name="investorObj"></param>
        ///// <param name="investorForeignObj"></param>
        ///// <returns></returns>
        //public static async Task<string> ReplaceTextGoogleDocs(DocsService docsService, invOrderContract iocItem, Google.Apis.Drive.v3.Data.File mergedFile)
        //{
        //    List<Request> requestsList = new List<Request>();

        //    #region replace text
        //    RequestGDocs(requestsList, "{{buyer.full_name}}", string.IsNullOrEmpty(iocItem.ioc_buy_full_name) ? " " : iocItem.ioc_buy_full_name);
        //    RequestGDocs(requestsList, "{{contract.contract_no}}", string.IsNullOrEmpty(iocItem.ioc_cont_no) ? " " : iocItem.ioc_cont_no);
        //    RequestGDocs(requestsList, "{{owner.name}}", string.IsNullOrEmpty(iocItem.ioc_own_name) ? " " : iocItem.ioc_own_name);
        //    RequestGDocs(requestsList, "{{owner.tax_cd}}", string.IsNullOrEmpty(iocItem.ioc_own_tax_cd) ? " " : iocItem.ioc_own_tax_cd);
        //    RequestGDocs(requestsList, "{{owner.brn_plc}}", string.IsNullOrEmpty(iocItem.ioc_own_brn_plc) ? " " : iocItem.ioc_own_brn_plc);
        //    RequestGDocs(requestsList, "{{owner.brn_last_at}}", string.IsNullOrEmpty(iocItem.ioc_own_brn_last_at) ? " " : DateTime.Parse(iocItem.ioc_own_brn_last_at).ToString("dd/MM/yyyy"));
        //    RequestGDocs(requestsList, "{{owner.address}}", string.IsNullOrEmpty(iocItem.ioc_own_address) ? " " : iocItem.ioc_own_address);
        //    RequestGDocs(requestsList, "{{owner.rep_name}}", string.IsNullOrEmpty(iocItem.ioc_own_rep_name) ? " " : iocItem.ioc_own_rep_name);
        //    RequestGDocs(requestsList, "{{owner.rep_position}}", string.IsNullOrEmpty(iocItem.ioc_own_rep_position) ? " " : iocItem.ioc_own_rep_position);
        //    RequestGDocs(requestsList, "{{owner.rep_authority_no}}", string.IsNullOrEmpty(iocItem.ioc_own_rep_authority_no) ? " " : iocItem.ioc_own_rep_authority_no);
        //    RequestGDocs(requestsList, "{{owner.rep_pass_at}}", string.IsNullOrEmpty(iocItem.ioc_own_rep_pass_at) ? " " : DateTime.Parse(iocItem.ioc_own_rep_pass_at).ToString("dd/MM/yyyy")); //
        //    RequestGDocs(requestsList, "{{owner.phone}}", string.IsNullOrEmpty(iocItem.ioc_own_phone) ? " " : iocItem.ioc_own_phone);

        //    RequestGDocs(requestsList, "{{buyer.resident_address}}", string.IsNullOrEmpty(iocItem.ioc_buy_resident_address) ? " " : iocItem.ioc_buy_resident_address);
        //    RequestGDocs(requestsList, "{{buyer.trading_address}}", string.IsNullOrEmpty(iocItem.ioc_buy_trading_address) ? " " : iocItem.ioc_buy_trading_address);
        //    RequestGDocs(requestsList, "{{buyer.pass_no}}", string.IsNullOrEmpty(iocItem.ioc_buy_pass_no) ? " " : iocItem.ioc_buy_pass_no);
        //    RequestGDocs(requestsList, "{{buyer.pass_plc}}", string.IsNullOrEmpty(iocItem.ioc_buy_pass_plc) ? " " : iocItem.ioc_buy_pass_plc);
        //    RequestGDocs(requestsList, "{{buyer.pass_at}}", string.IsNullOrEmpty(iocItem.ioc_buy_pass_at) ? " " : DateTime.Parse(iocItem.ioc_buy_pass_at).ToString("dd/MM/yyyy"));
        //    RequestGDocs(requestsList, "{{buyer.tax_no}}", string.IsNullOrEmpty(iocItem.ioc_buy_tax_no) ? " " : iocItem.ioc_buy_tax_no);
        //    RequestGDocs(requestsList, "{{buyer.phone}}", string.IsNullOrEmpty(iocItem.ioc_buy_phone) ? " " : iocItem.ioc_buy_phone);
        //    RequestGDocs(requestsList, "{{buyer.email}}", string.IsNullOrEmpty(iocItem.ioc_buy_email) ? " " : iocItem.ioc_buy_email);
        //    RequestGDocs(requestsList, "{{buyer.bank_account_no}} ", string.IsNullOrEmpty(iocItem.ioc_buy_bank_account_no) ? " " : iocItem.ioc_buy_bank_account_no);
        //    RequestGDocs(requestsList, "{{buyer.bank_name}}", string.IsNullOrEmpty(iocItem.ioc_buy_bank_name) ? " " : iocItem.ioc_buy_bank_name);
        //    RequestGDocs(requestsList, "{{buyer.bank_branch}}", string.IsNullOrEmpty(iocItem.ioc_buy_bank_branch) ? " " : iocItem.ioc_buy_bank_branch);
        //    RequestGDocs(requestsList, "{{buyer.bank_account_name}}", string.IsNullOrEmpty(iocItem.ioc_buy_bank_account_name) ? " " : iocItem.ioc_buy_bank_account_name);
        //    RequestGDocs(requestsList, "{{invest_base_amount}}", iocItem.ioc_inv_base_amt.Equals(null) ? "0" : double.Parse(iocItem.ioc_inv_base_amt.ToString()).ToString("#,###", CultureInfo.GetCultureInfo("vi-VN")));
        //    RequestGDocs(requestsList, "{{invest_base_amount_text}}",  MoneyHelper.ConvertMoneyToText(iocItem.ioc_inv_base_amt.Equals(null) ? 0 : iocItem.ioc_inv_base_amt));

        //    RequestGDocs(requestsList, "{{invested_period_month}}", iocItem.ioc_inv_tenor.Equals(null) ? "0" : double.Parse(iocItem.ioc_inv_tenor.ToString()).ToString("#,###", CultureInfo.GetCultureInfo("vi-VN")));
        //    RequestGDocs(requestsList, "{{invested_at}}", string.IsNullOrEmpty(iocItem.ioc_cont_start_at) ? " " : iocItem.ioc_cont_start_at);
        //    RequestGDocs(requestsList, "{{invested_ended_at}}", string.IsNullOrEmpty(iocItem.ioc_cont_end_at) ? " " : iocItem.ioc_cont_end_at);
        //    RequestGDocs(requestsList, "{{room.room_category.type}}", string.IsNullOrEmpty(iocItem.ioc_room_type) ? " " : iocItem.ioc_room_type);
        //    RequestGDocs(requestsList, "{{room.room_code}}", string.IsNullOrEmpty(iocItem.ioc_room_code) ? " " : iocItem.ioc_room_code);
        //    //RequestGDocs(requestsList, "{{ioc_room_base_amt}}", iocItem.ioc_inv_base_amt.Equals(null) ? "0" : double.Parse(iocItem.ioc_inv_base_amt.ToString()).ToString("#,###", CultureInfo.GetCultureInfo("vi-VN")));
        //    RequestGDocs(requestsList, "{{ioc_room_base_amt}}", iocItem.ioc_room_inv_amt.Equals(null) ? "0" : double.Parse(iocItem.ioc_room_inv_amt.ToString()).ToString("#,###", CultureInfo.GetCultureInfo("vi-VN")));
        //    RequestGDocs(requestsList, "{{ioc_room_inv_amt}}", iocItem.ioc_room_inv_amt.Equals(null) ? "0" : double.Parse(iocItem.ioc_room_inv_amt.ToString()).ToString("#,###", CultureInfo.GetCultureInfo("vi-VN")));
            
        //    RequestGDocs(requestsList, "{{room.room_category.area_water}}", iocItem.ioc_room_area_wat.Equals(null) ? "0" : iocItem.ioc_room_area_wat.ToString().Replace(".", ","));
        //    RequestGDocs(requestsList, "{{project.name}}", string.IsNullOrEmpty(iocItem.ioc_room_proj_name) ? " " : iocItem.ioc_room_proj_name);
        //    RequestGDocs(requestsList, "{{project.address}}", string.IsNullOrEmpty(iocItem.ioc_room_proj_address) ? " " : iocItem.ioc_room_proj_address);
        //    RequestGDocs(requestsList, "{{project.investor_name}}", string.IsNullOrEmpty(iocItem.ioc_room_investor_name) ? " " : iocItem.ioc_room_investor_name);
        //    RequestGDocs(requestsList, "{{interested_rate}}", iocItem.ioc_inv_base_rt.Equals(null) ? "0" : (iocItem.ioc_inv_base_rt * 100).ToString("G9").Replace(".", ","));
        //    RequestGDocs(requestsList, "{{interested_sale_discount}}", iocItem.ioc_inv_buyback_rt.Equals(null) ? "0" : (iocItem.ioc_inv_buyback_rt * 100).ToString("G9").Replace(".", ","));
        //    RequestGDocs(requestsList, "{{referral.referral_code}}", string.IsNullOrEmpty(iocItem.ioc_refer_code) ? " " : iocItem.ioc_refer_code);
        //    RequestGDocs(requestsList, "{{referral.referral_name}}", string.IsNullOrEmpty(iocItem.ioc_refer_name) ? " " : iocItem.ioc_refer_name);
        //    RequestGDocs(requestsList, "{{investment_opening.advance_max_rate}}", iocItem.ioc_adv_max_rt.Equals(null) ? "0" : (iocItem.ioc_adv_max_rt * 100).ToString("G9").Replace(".", ","));
        //    #endregion

        //    var body = new BatchUpdateDocumentRequest();
        //    body.Requests = requestsList;
        //    var batchUpdateRequest = docsService.Documents.BatchUpdate(body, mergedFile.Id);
        //    var result = await batchUpdateRequest.ExecuteAsync();

        //    return result.DocumentId;
        //}

    }
}
