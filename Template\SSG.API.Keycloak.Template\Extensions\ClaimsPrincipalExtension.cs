﻿using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using Newtonsoft.Json;
using SSG.API.Keycloak.Template.Authorization;

namespace SSG.API.Keycloak.Template.Extensions
{
    public static class ClaimsPrincipalExtension
    {
        public static string GetUserId(this ClaimsPrincipal user)
        {
            return user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }

        public static string GetUserName(this ClaimsPrincipal user)
        {
            return user.Claims.FirstOrDefault(e => e.Type.Equals("preferred_username"))?.Value;
        }

        public static List<Permission> GetPermissions(this ClaimsPrincipal user)
        {
            var claim = user.Claims.FirstOrDefault(e => e.Type.Equals("authorization"));
            if (claim == null || string.IsNullOrEmpty(claim.Value)) return null;
            var authorization = JsonConvert.DeserializeObject<Authorization.Authorization>(claim.Value);
            return authorization.Permissions;
        }

        public static List<Permission> GetPermissions(this ClaimsPrincipal user, string accessToken)
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadToken(accessToken);
            var securityToken = jsonToken as JwtSecurityToken;
            var claimsIdentity = new ClaimsIdentity(securityToken?.Claims);
            user = new ClaimsPrincipal(claimsIdentity);
            return user.GetPermissions();
        }

        public static IEnumerable<string> GetRoles(this ClaimsPrincipal user)
        {
            var roles = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value);

            return roles;
        }
    }
}