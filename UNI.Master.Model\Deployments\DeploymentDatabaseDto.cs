using System;
using UNI.Model;

namespace UNI.Master.Model.Deployments
{
    /// <summary>
    /// Deployment Database DTO
    /// </summary>
    public class DeploymentDatabaseDto
    {
        public Guid Id { get; set; }
        public Guid DeploymentId { get; set; }
        public Guid DatabaseId { get; set; }
        public Guid CustomerId { get; set; }
        public Guid ProductId { get; set; }
        public string DatabaseName { get; set; } = null!;
        public string ConnectionString { get; set; } = null!;
        public string Status { get; set; } = null!;
        public DateTime Created { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? Updated { get; set; }
        public Guid? UpdatedBy { get; set; }
    }

    /// <summary>
    /// Deployment Database Info
    /// </summary>
    public class DeploymentDatabaseInfo : viewBaseInfo
    {
        public Guid? Id { get; set; }
        public Guid? DeploymentId { get; set; }
        public Guid? DatabaseId { get; set; }
        public Guid? CustomerId { get; set; }
        public Guid? ProductId { get; set; }
        public string DatabaseName { get; set; }
        public string ConnectionString { get; set; }
        public string Status { get; set; }
        public string CustomerName { get; set; }
        public string ProductName { get; set; }
        public string InstanceName { get; set; }
        public string SchemaVersion { get; set; }
    }

    /// <summary>
    /// Deployment Database Filter
    /// </summary>
    public class DeploymentDatabaseFilter : FilterInput
    {
        public Guid? CustomerId { get; set; }
        public Guid? ProductId { get; set; }
        public Guid? DeploymentId { get; set; }
        public string Status { get; set; }
        public DateTime? CreatedFrom { get; set; }
        public DateTime? CreatedTo { get; set; }
    }

    /// <summary>
    /// Create Deployment Database Request
    /// </summary>
    public class CreateDeploymentDatabaseRequest
    {
        public Guid CustomerId { get; set; }
        public Guid ProductId { get; set; }
        public Guid? DeploymentId { get; set; }
        public string DatabaseName { get; set; } = null!;
        public Guid? DatabaseInstanceId { get; set; }
        public Guid? SchemaVersionId { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// Update Deployment Database Request
    /// </summary>
    public class UpdateDeploymentDatabaseRequest
    {
        public Guid Id { get; set; }
        public string DatabaseName { get; set; }
        public string ConnectionString { get; set; }
        public string Status { get; set; }
        public Guid? SchemaVersionId { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// Database Schema Migration Request
    /// </summary>
    public class DatabaseSchemaMigrationRequest
    {
        public Guid DatabaseId { get; set; }
        public Guid FromVersionId { get; set; }
        public Guid ToVersionId { get; set; }
        public bool BackupBeforeMigration { get; set; } = true;
        public string Notes { get; set; }
    }

    /// <summary>
    /// Deployment Database Status
    /// </summary>
    public static class DeploymentDatabaseStatus
    {
        public const string Creating = "CREATING";
        public const string Active = "ACTIVE";
        public const string Migrating = "MIGRATING";
        public const string Error = "ERROR";
        public const string Inactive = "INACTIVE";
        public const string Deleted = "DELETED";
    }
}
