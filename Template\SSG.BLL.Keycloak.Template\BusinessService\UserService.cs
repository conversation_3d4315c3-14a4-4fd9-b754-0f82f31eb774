﻿using Microsoft.Extensions.Logging;
using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces;
using SSG.DAL.Keycloak.Template.Interfaces;

namespace SSG.BLL.Keycloak.Template.BusinessService
{
    public class UserService:IUserService
    {
        private readonly ILogger<UserService> _logger;
        private readonly IUserRepository _repository;

        public UserService(IUserRepository repository, ILogger<UserService> logger)
        {
            _logger = logger;
            _repository = repository;
        }

        //public async Task<YmUserDataPage> GetDataPageAsync(YmUserDataFilter query)
        //{
        //    return await _repository.GetDataPageAsync(query);
        //}

        //public async Task<BaseValidate> SetUserDataAsync(string userId, YmUserData data)
        //{
        //    return await _repository.SetUserDataAsync(userId, data);
        //}

        //public async Task<bool> SetUserAsync(YmUserLogin userLogin)
        //{
        //    try
        //    {
        //        return await _repository.SetUserAsync(userLogin);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex}");
        //        throw;
        //    }
        //}
    }
}
