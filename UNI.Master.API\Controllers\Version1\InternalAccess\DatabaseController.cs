using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using UNI.Master.BLL.Interfaces.Database;
using UNI.Master.Model.Database;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1.InternalAccess
{

    /// <summary>
    /// 
    /// </summary>
    [Route("api/v1/[controller]")]
    [Authorize]
    public class DatabaseController : UniController
    {
        private readonly IDatabaseService _databaseService;
        /// <inheritdoc />
        public DatabaseController(
            ILoggerFactory logger, IDatabaseService databaseService) : base(logger)
        {
            _databaseService = databaseService;
        }
        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="database"></param>
        ///// <returns></returns>
        //public async Task<IActionResult> Post([FromBody] DatabaseDto database)
        //{

        //}

        /// <summary>
        /// get db instances
        /// </summary>
        /// <returns></returns>
        [HttpGet("instances")]
        public async Task<IActionResult> GetInstances()
        {
            IEnumerable<DatabaseInstanceDto> rs = await _databaseService.GetInstancesAsync();
            return Ok(rs);
        }

    }
}

