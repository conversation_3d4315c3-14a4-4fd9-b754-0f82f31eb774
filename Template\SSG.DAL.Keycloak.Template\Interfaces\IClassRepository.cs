﻿using System;
using System.Threading.Tasks;
using SSG.Model;

namespace SSG.DAL.Keycloak.Template.Interfaces
{
    public interface IClassRepository
    {
        //Task<YmClassPage> GetPageAsync(FilterBase query);
        //Task<YmClassInfo> GetInfoAsync(string userId, Guid? id);
        //Task<BaseValidate> SetInfoAsync(string userId, YmClassInfo info);
        Task<BaseValidate> DeleteAsync(string userId, Guid? id);
        //Task<BaseResponse<ImportClassResponse>> Import(string userId, string xml);
    }
}
