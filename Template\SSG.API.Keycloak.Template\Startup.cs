﻿using AutoMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Serilog;
using SSG.API.Keycloak.Template.Attributes;
using SSG.API.Keycloak.Template.Authorization;
using SSG.API.Keycloak.Template.Extensions;
using SSG.BLL.Keycloak.Template;
using SSG.Common;
using SSG.Common.Middleware;
using SSG.Model;
using System;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace SSG.API.Keycloak.Template
{
    /// <summary>
    /// Startup class
    /// </summary>
    public class Startup
    {
        private readonly IWebHostEnvironment _env;
        /// <summary>
        /// Startup contructor
        /// </summary>
        /// <param name="env"></param>
        public Startup(IWebHostEnvironment env)
        {
            _env = env;
            Common.Utils.SetEnvironmentVariable(true);
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true)
                .AddEnvironmentVariables();
            Configuration = builder.Build();
            ConfigManager.SetConfiguration(Configuration);
        }
        /// <summary>
        /// Configuration interface
        /// </summary>
        public IConfigurationRoot Configuration { get; }

        /// <summary>
        /// This method gets called by the runtime. Use this method to add services to the container.
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.RequireHttpsMetadata = false;
                    options.Authority = Configuration["Jwt:Authority"];
                    options.Audience = Configuration["Jwt:ClientId"];
                    options.IncludeErrorDetails = true;
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ClockSkew = TimeSpan.Zero,
                        ValidateAudience = Convert.ToBoolean(Configuration["Jwt:ValidateAudience"]),
                        ValidAudiences = Configuration["Jwt:ValidAudiences"]?.Split(";", StringSplitOptions.RemoveEmptyEntries),
                        ValidateIssuer = Convert.ToBoolean(Configuration["Jwt:ValidateIssuer"]),
                        ValidIssuer = Configuration["Jwt:ValidIssuer"],
                        ValidateLifetime = true,
                        RequireExpirationTime = true,
                        ValidateIssuerSigningKey = true,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes("123456"))
                    };
                    options.Events = new JwtBearerEvents
                    {
                        OnTokenValidated = context =>
                        {
                            MapRealmRoles(context);
                            return Task.CompletedTask;
                        }
                    };
                });

            services.AddControllers(options =>
                {
                    options.Filters.Add(typeof(AuthorizeAttribute));
                })
                .AddNewtonsoftJson();

            services.AddSwaggerDocument(o => o.Title = "API Keycloak Template");

            services.RegisterServices(Configuration);
            var config = new MapperConfiguration(cfg => { cfg.AddProfile(new EntityModelMapperProfile()); });
            services.AddSingleton<IMapper>(sp => config.CreateMapper());
            services.AddSingleton<IFileProvider>(_env.WebRootFileProvider);
            services.AddSingleton<IWebHostEnvironment>(_env);

            services.Configure<AppSettings>(Configuration.GetSection("AppSettings"));
        }
        /// <summary>
        /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="logger"></param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory logger)
        {
            app.UseSerilogRequestLogging();
            app.UseHttpsRedirection();
            app.UseDefaultFiles();
            app.UseStaticFiles();

            app.UseRouting();
            app.UseOpenApi();
            app.UseSwaggerUi3();

            app.UseCors(builder =>
                builder.AllowAnyOrigin()
                    .AllowAnyHeader()
                    .AllowAnyMethod());
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseMiddleware<ErrorHandlerMiddleware>();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute("api", "api/v{version}/{controller}/{action}/{id?}");
            });
        }
        private static void MapRealmRoles(TokenValidatedContext context)
        {
            if (!(context.Principal.Identity is ClaimsIdentity claimsIdentity)) return;
            var realmAccess = context.Principal.Claims.FirstOrDefault(w => w.Type == "realm_access");
            if (realmAccess == null) return;
            var realmRole = JsonConvert.DeserializeObject<Role>(realmAccess.Value);
            foreach (var role in realmRole.Roles)
            {
                claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, role));
            }
        }
    }
}
