{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"UNI.Utils/1.0.0": {"dependencies": {"Aspose.Cells": "24.1.0", "Aspose.PDF": "24.1.0", "FreeSpire.Doc": "12.2.0", "Google.Apis.Docs.v1": "1.51.0.2252", "Google.Apis.Drive.v3": "1.51.0.2265", "Google.Cloud.Storage.V1": "3.4.0", "Markdig": "0.37.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.1.3", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.3.1", "Microsoft.NETCore.Runtime": "1.0.1", "MiniWord": "0.6.1", "Newtonsoft.Json": "13.0.3", "QRCoder": "1.4.1", "ReverseMarkdown": "4.4.0", "Serilog": "3.1.1", "SimpleHelpers.MemoryCache": "1.1.1", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.80.3", "System.ComponentModel.Primitives": "4.3.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Linq": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Security.Cryptography.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "TMS.FlexCel": "7.6.4", "runtime.native.System.Security.Cryptography": "4.3.0", "Aspose.Words": "20.6.0.0", "Aspose.Words.Pdf2Word": "20.6.0.0"}, "runtime": {"UNI.Utils.dll": {}}}, "Aspose.Cells/24.1.0": {"dependencies": {"SkiaSharp": "2.88.6", "System.Security.Cryptography.Pkcs": "6.0.4", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/net6.0/Aspose.Cells.dll": {"assemblyVersion": "24.1.0.0", "fileVersion": "24.1.0.0"}}}, "Aspose.PDF/24.1.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Diagnostics.PerformanceCounter": "6.0.1", "System.Drawing.Common": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.4", "System.Security.Permissions": "6.0.0", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/net6.0/Aspose.PDF.dll": {"assemblyVersion": "24.1.0.0", "fileVersion": "24.1.0.0"}}}, "DocumentFormat.OpenXml/2.18.0": {"dependencies": {"System.IO.Packaging": "4.7.0"}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "2.18.0.0", "fileVersion": "2.18.0.0"}}}, "FreeSpire.Doc/12.2.0": {"dependencies": {"HarfBuzzSharp": "*******", "System.Drawing.Common": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.4", "System.Security.Cryptography.Xml": "6.0.0", "System.Security.Permissions": "6.0.0", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/net6.0/Spire.Doc.dll": {"assemblyVersion": "12.2.0.0", "fileVersion": "12.2.0.3360"}}}, "Google.Api.Gax/3.2.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Google.Api.Gax.Rest/3.2.0": {"dependencies": {"Google.Api.Gax": "3.2.0", "Google.Apis.Auth": "1.51.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Google.Apis/1.51.0": {"dependencies": {"Google.Apis.Core": "1.51.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.dll": {"assemblyVersion": "1.51.0.0", "fileVersion": "1.51.0.0"}}}, "Google.Apis.Auth/1.51.0": {"dependencies": {"Google.Apis": "1.51.0", "Google.Apis.Core": "1.51.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.Auth.PlatformServices.dll": {"assemblyVersion": "1.51.0.0", "fileVersion": "1.51.0.0"}, "lib/netstandard2.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.51.0.0", "fileVersion": "1.51.0.0"}}}, "Google.Apis.Core/1.51.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Apis.Core.dll": {"assemblyVersion": "1.51.0.0", "fileVersion": "1.51.0.0"}}}, "Google.Apis.Docs.v1/1.51.0.2252": {"dependencies": {"Google.Apis": "1.51.0", "Google.Apis.Auth": "1.51.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.Docs.v1.dll": {"assemblyVersion": "1.51.0.2252", "fileVersion": "1.51.0.2252"}}}, "Google.Apis.Drive.v3/1.51.0.2265": {"dependencies": {"Google.Apis": "1.51.0", "Google.Apis.Auth": "1.51.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.Drive.v3.dll": {"assemblyVersion": "1.51.0.2265", "fileVersion": "1.51.0.2265"}}}, "Google.Apis.Storage.v1/1.49.0.2151": {"dependencies": {"Google.Apis": "1.51.0", "Google.Apis.Auth": "1.51.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.Storage.v1.dll": {"assemblyVersion": "1.49.0.2151", "fileVersion": "1.49.0.2151"}}}, "Google.Cloud.Storage.V1/3.4.0": {"dependencies": {"Google.Api.Gax.Rest": "3.2.0", "Google.Apis.Storage.v1": "1.49.0.2151"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Storage.V1.dll": {"assemblyVersion": "3.4.0.0", "fileVersion": "3.4.0.0"}}}, "HarfBuzzSharp/*******": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/HarfBuzzSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HtmlAgilityPack/1.11.61": {"runtime": {"lib/netstandard2.0/HtmlAgilityPack.dll": {"assemblyVersion": "1.11.61.0", "fileVersion": "1.11.61.0"}}}, "Markdig/0.37.0": {"runtime": {"lib/net6.0/Markdig.dll": {"assemblyVersion": "0.37.0.0", "fileVersion": "0.37.0.0"}}}, "Microsoft.AspNetCore.Antiforgery/2.1.1": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Authentication.Core/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Authorization/2.1.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.1", "Microsoft.AspNetCore.Authorization": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.DataProtection/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.1.1", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Win32.Registry": "4.5.0", "System.Security.Cryptography.Xml": "6.0.0", "System.Security.Principal.Windows": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Html.Abstractions/2.1.1": {"dependencies": {"System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Net.Http.Headers": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Http.Features/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.JsonPatch/2.1.1": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Core/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.1", "Microsoft.AspNetCore.Authorization.Policy": "2.1.1", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.3", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.1", "Microsoft.AspNetCore.Routing": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.DependencyModel": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Threading.Tasks.Extensions": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.Extensions.Localization": "2.1.1", "System.ComponentModel.Annotations": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.1.3": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.1.3": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.1.1", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.1.1", "Microsoft.AspNetCore.Html.Abstractions": "2.1.1", "Microsoft.AspNetCore.Mvc.Core": "2.1.3", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.3", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.1.3", "Microsoft.Extensions.WebEncoders": "2.1.1", "Newtonsoft.Json.Bson": "1.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.3.18263"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Routing/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.ObjectPool": "2.1.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"dependencies": {"Microsoft.Net.Http.Headers": "2.1.1", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.46214"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyModel/6.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Localization/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Localization.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Localization.Abstractions/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.ObjectPool/2.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.WebEncoders/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.IdentityModel.Abstractions/7.3.1": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.3.1"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Logging/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.3.1"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Protocols/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.3.1", "Microsoft.IdentityModel.Tokens": "7.3.1"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.3.1", "System.IdentityModel.Tokens.Jwt": "7.3.1"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.50203"}}}, "Microsoft.IdentityModel.Tokens/7.3.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.3.1"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.50203"}}}, "Microsoft.Net.Http.Headers/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.NETCore.Platforms/2.0.0": {}, "Microsoft.NETCore.Runtime/1.0.1": {"dependencies": {"Microsoft.NETCore.Runtime.CoreCLR": "1.0.1", "Microsoft.NETCore.Runtime.Native": "1.0.1"}}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.1": {}, "Microsoft.NETCore.Runtime.Native/1.0.1": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.5.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "4.5.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MiniWord/0.6.1": {"dependencies": {"DocumentFormat.OpenXml": "2.18.0"}, "runtime": {"lib/net5.0/MiniWord.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.1": {"dependencies": {"NETStandard.Library": "1.6.1", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard1.3/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.1.20722"}}}, "QRCoder/1.4.1": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/netstandard2.0/QRCoder.dll": {"assemblyVersion": "1.4.1.0", "fileVersion": "1.4.1.0"}}}, "ReverseMarkdown/4.4.0": {"dependencies": {"HtmlAgilityPack": "1.11.61"}, "runtime": {"lib/net6.0/ReverseMarkdown.dll": {"assemblyVersion": "4.4.0.0", "fileVersion": "4.4.0.0"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/debian.8-x64/native/System.Security.Cryptography.Native.so": {"rid": "debian.8-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.so": {"rid": "fedora.23-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.so": {"rid": "fedora.24-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "4.3.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.1": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.1"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.so": {"rid": "opensuse.13.2-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.so": {"rid": "opensuse.42.1-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.dylib": {"rid": "osx.10.10-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.1": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.so": {"rid": "rhel.7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.so": {"rid": "ubuntu.14.04-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.so": {"rid": "ubuntu.16.04-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography/4.3.0": {"runtimeTargets": {"runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.so": {"rid": "ubuntu.16.10-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "Serilog/3.1.1": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "3.1.1.0"}}}, "SimpleHelpers.MemoryCache/1.1.1": {"dependencies": {"SimpleHelpers.NamedLock": "1.0.3"}}, "SimpleHelpers.NamedLock/1.0.3": {}, "SkiaSharp/2.88.6": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.6.0"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.80.3": {"dependencies": {"SkiaSharp": "2.88.6"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.1": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/4.5.0": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Configuration.ConfigurationManager/8.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.424.16909"}}}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/6.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.3.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.3.1", "Microsoft.IdentityModel.Tokens": "7.3.1"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.3.1.0", "fileVersion": "7.3.1.50203"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Packaging/4.7.0": {"runtime": {"lib/netstandard2.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.1", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/6.0.4": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1923.31806"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1923.31806"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.4"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.1": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.1"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "TMS.FlexCel/7.6.4": {"dependencies": {"SkiaSharp": "2.88.6", "System.Security.Cryptography.Pkcs": "6.0.4", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"lib/netcoreapp3.1/FlexCel.dll": {"assemblyVersion": "7.6.4.0", "fileVersion": "7.6.4.0"}}}, "Aspose.Words/20.6.0.0": {"runtime": {"Aspose.Words.dll": {"assemblyVersion": "20.6.0.0", "fileVersion": "20.6.0.0"}}}, "Aspose.Words.Pdf2Word/20.6.0.0": {"runtime": {"Aspose.Words.Pdf2Word.dll": {"assemblyVersion": "20.6.0.0", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"UNI.Utils/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspose.Cells/24.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-qw4TIXPoGvfne9Q9vSkDO0nJPLiEeNuLQj2CzUXe5pSbXUwJdgtmSKqFZJ8qchNjmGq4ElCfYTu4WwMdSSUZJg==", "path": "aspose.cells/24.1.0", "hashPath": "aspose.cells.24.1.0.nupkg.sha512"}, "Aspose.PDF/24.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jDic58t9nAhBY42acBbaqL02/LhB8Auib9o5GvN7oFo9FgU8WZ7ApCiLRbv6zsh5uRu+EKElPOh5jmJj/RrY0A==", "path": "aspose.pdf/24.1.0", "hashPath": "aspose.pdf.24.1.0.nupkg.sha512"}, "DocumentFormat.OpenXml/2.18.0": {"type": "package", "serviceable": true, "sha512": "sha512-5fmehgA7WmJD551QwBhvjSsEmMbCQ/yfW83ZLfbPx1TeQGm0ikGIwAvz/XfO4veCjYWpBQfFH9elVhC2Y+HatQ==", "path": "documentformat.openxml/2.18.0", "hashPath": "documentformat.openxml.2.18.0.nupkg.sha512"}, "FreeSpire.Doc/12.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qqc9tf1utZAQoQPfOFUXTXT0C27XZAqathA8/8jk991r/fi/NlpG5wQFgWJZ6v2zFftouuaRI7jby0pjPCjaKw==", "path": "freespire.doc/12.2.0", "hashPath": "freespire.doc.12.2.0.nupkg.sha512"}, "Google.Api.Gax/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-0<PERSON>jahFAHTOoprSgvJiQ6/fIQLrUYU4QIFgkuJ51/lcmhZbuXxB3ycPk3JTVEvx6A5yQBL14wgmHgwXLcEsnu3Q==", "path": "google.api.gax/3.2.0", "hashPath": "google.api.gax.3.2.0.nupkg.sha512"}, "Google.Api.Gax.Rest/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YY4mD0nGxTx1uez7Perm+zAd3FH50dd3+7HTYsRFCywDEtj3RkrMjcAmw6mNpKkw2sRICu7aYNy1mgMjd3nVbw==", "path": "google.api.gax.rest/3.2.0", "hashPath": "google.api.gax.rest.3.2.0.nupkg.sha512"}, "Google.Apis/1.51.0": {"type": "package", "serviceable": true, "sha512": "sha512-4QL7S1PnDN/VumfzrLOGw8tiFLYRL/oPVQkCuIVltAh/gSPWxLu7UFZeum4S3B/ry7794Wigg53jclLlYX/haQ==", "path": "google.apis/1.51.0", "hashPath": "google.apis.1.51.0.nupkg.sha512"}, "Google.Apis.Auth/1.51.0": {"type": "package", "serviceable": true, "sha512": "sha512-rwyLkiXev6esCRXq+srUcEb8zWylNg7eyE9zO8GxiQplrn1+Jhn8WlwhyJ/xXM83Mrd7Fi0p/UpnOo+m8NBI2g==", "path": "google.apis.auth/1.51.0", "hashPath": "google.apis.auth.1.51.0.nupkg.sha512"}, "Google.Apis.Core/1.51.0": {"type": "package", "serviceable": true, "sha512": "sha512-2HBwN81SB5DBI/TKbJr6y6u2I5sBlLxUHYUkc26Y4FSFachdDlWISZZreO0AVKdJrHsbmKDhHdqkRjkLSGeiNw==", "path": "google.apis.core/1.51.0", "hashPath": "google.apis.core.1.51.0.nupkg.sha512"}, "Google.Apis.Docs.v1/1.51.0.2252": {"type": "package", "serviceable": true, "sha512": "sha512-VBRUjVtY/D4ukw4RPA4somZgCvx5bNQ8rou8Sgc61RUuONJMGnn2WnUDltGcv0+jZTr/cMy0I0byPD4u381Jvg==", "path": "google.apis.docs.v1/1.51.0.2252", "hashPath": "google.apis.docs.v1.1.51.0.2252.nupkg.sha512"}, "Google.Apis.Drive.v3/1.51.0.2265": {"type": "package", "serviceable": true, "sha512": "sha512-COlhfJ0tq47se3ST0PaEaJMuiJPBvTLnCtPO3t7R/0Zhp/3M+tMj0ruRUuk3pYXg182B5zLpdbZ1VI90pm+e5Q==", "path": "google.apis.drive.v3/1.51.0.2265", "hashPath": "google.apis.drive.v3.1.51.0.2265.nupkg.sha512"}, "Google.Apis.Storage.v1/1.49.0.2151": {"type": "package", "serviceable": true, "sha512": "sha512-3tV0DZLCgbX0OWQKvkipIqoayJBohH7T0ACrUX/glJ9hqJbMQq39hhLqZEQ2hySJsOrIEKoHWBahN6H5CNg4Ow==", "path": "google.apis.storage.v1/1.49.0.2151", "hashPath": "google.apis.storage.v1.1.49.0.2151.nupkg.sha512"}, "Google.Cloud.Storage.V1/3.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-vDw61R3rVSgjr65/OfOQ42Ce0/1M8M/L1K09Bxbr4gOIUMtgt4nFfTa6ZowoSxshrYB934nZo58lvbWLxkR/Pg==", "path": "google.cloud.storage.v1/3.4.0", "hashPath": "google.cloud.storage.v1.3.4.0.nupkg.sha512"}, "HarfBuzzSharp/*******": {"type": "package", "serviceable": true, "sha512": "sha512-PvPJf3GGhHTysfyzkbYXU3v3ERLwIjyCjRfQ0MJP5c7kLXsBEvcESw04gqUmpoM7ifLx8UvCSGnLDHlVqQe49Q==", "path": "harfbuzzsharp/*******", "hashPath": "harfbuzzsharp.*******.nupkg.sha512"}, "HtmlAgilityPack/1.11.61": {"type": "package", "serviceable": true, "sha512": "sha512-hqZASeEFHS9quHvfZSwaULoAJLWkOYVPiQjc3P9J4pQS8vSYzrP3bxe04Vm7vYYuxwbQhq9hCSVvZVLTyRaNaQ==", "path": "htmlagilitypack/1.11.61", "hashPath": "htmlagilitypack.1.11.61.nupkg.sha512"}, "Markdig/0.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-biiu4MTPFjW55qw6v5Aphtj0MjDLJ14x8ndZwkJUHIeqvaSGKeqhLY7S7Vu/S3k7/c9KwhhnaCDP9hdFNUhcNA==", "path": "markdig/0.37.0", "hashPath": "markdig.0.37.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-De4NysQJXeWiyzjCH+zE+hVeB7mgCelz00zsBFqkrFtgLWaint5Xt/4qACxRVLUGHQsUo48V6lG0entMJMwv3Q==", "path": "microsoft.aspnetcore.antiforgery/2.1.1", "hashPath": "microsoft.aspnetcore.antiforgery.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Smj5TGeE9629+hGHPk/DZUfCMYGvQwCajAsU/OVExRb8JXfeua4uXZFzT9Kh3pJY2MThPSt1lbDnkL2KaDyw/A==", "path": "microsoft.aspnetcore.authentication.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zo6SLzqxrW0PFg1AB0xSb+Rta4hCuX8hgOY425ldhFq4kKcmw45oJQ2zOIeeW/6EuBtEy+hwDB96baxTmXtfeA==", "path": "microsoft.aspnetcore.authentication.core/2.1.1", "hashPath": "microsoft.aspnetcore.authentication.core.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rsxgcq+BU7VDGOZ0DdyPQOSE+jw5Bb4nk6PQpG70U/ZhgKFaAnnLeEnCfHgnCBUy3kn2ZtH3ZKJL+sh9MYzR4w==", "path": "microsoft.aspnetcore.authorization/2.1.1", "hashPath": "microsoft.aspnetcore.authorization.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6Gy9rFN1/4pKgjcbb2yaOmwpjV282dGnl7ewcCvcLxQmywpolkwxe5PPI6K/VPC2sovL5BtzhxnRl3OkwJZxwg==", "path": "microsoft.aspnetcore.authorization.policy/2.1.1", "hashPath": "microsoft.aspnetcore.authorization.policy.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-guY3jMNkcUi2hrMJ4/vPnUUFwudxTVSJ809gCfpq+xR0UgV6P9ZHZLOI5q/07QHDZY+kKPXxipXGyJXQpq2k0g==", "path": "microsoft.aspnetcore.cryptography.internal/2.1.1", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-OPZDPAAL3OwOCrz870F9goq//NJOmPl4Lv3dz+v0cRQe8EpsbCe0c6IRI8vdlFwM13Qy57D5rLQlysb+tLpENA==", "path": "microsoft.aspnetcore.dataprotection/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dcH52SMIIUOwBeDZ2QQEY3hWXJz50Dk2YzC/B2hxDLB78Il75BHGOhClIw6/0H+dKZCwItUytxoMNYtCSmG+aQ==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-W4V3uJY3mIUZbmon6MKOVr16r/NPgn/ey06L+BKf6uzXPua1Tzwlkz5h101b/Ncaown0iEJz5Pm6heYj+Fr/WQ==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-76cKcp2pWhvdV2TXTqMg/DyW7N6cDzTEhtL8vVWFShQN+Ylwv3eO/vUQr2BS3Hz4IZHEpL+FOo2T+MtymHDqDQ==", "path": "microsoft.aspnetcore.hosting.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+vD7HJYzAXNq17t+NgRkpS38cxuAyOBu8ixruOiA3nWsybozolUdALWiZ5QFtGRzajSLPFA2YsbO3NPcqoUwcw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CS/2N0d0JUdhYOrnd9Ll6O2Lb++CQaToKem6NyF+9RIgdL3tEZJOJHXcFWSXUSDqML98XQzbtnV+dCT22cBrRw==", "path": "microsoft.aspnetcore.html.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.html.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-pPDcCW8spnyibK3krpxrOpaFHf5fjV6k1Hsl6gfh77N/8gRYlLU7MOQDUnjpEwdlHmtxwJKQJNxZqVQOmJGRUw==", "path": "microsoft.aspnetcore.http/2.1.1", "hashPath": "microsoft.aspnetcore.http.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kQUEVOU4loc8CPSb2WoHFTESqwIa8Ik7ysCBfTwzHAd0moWovc9JQLmhDIHlYLjHbyexqZAlkq/FPRUZqokebw==", "path": "microsoft.aspnetcore.http.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.http.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ncAgV+cqsWSqjLXFUTyObGh4Tr7ShYYs3uW8Q/YpRwZn7eLV7dux5Z6GLY+rsdzmIHiia3Q2NWbLULQi7aziHw==", "path": "microsoft.aspnetcore.http.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.http.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VklZ7hWgSvHBcDtwYYkdMdI/adlf7ebxTZ9kdzAhX+gUs5jSHE9mZlTamdgf9miSsxc1QjNazHXTDJdVPZKKTw==", "path": "microsoft.aspnetcore.http.features/2.1.1", "hashPath": "microsoft.aspnetcore.http.features.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VjTsHQQG5H8Gjw6oi3jLUc6Wnc9Gnj1alQIwVsbfxuoXS5j0rTpzIKcRNyppEf0eQfI5fV/IDPJxgxV0NK5Xgw==", "path": "microsoft.aspnetcore.jsonpatch/2.1.1", "hashPath": "microsoft.aspnetcore.jsonpatch.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-G0oQiJ9okq+QbH9HBbmxu8/+Vhv063Dt06RzJPzsw7/uFT7Tvq5XHU5LI3b9qudyotJIRfYBbJRNeZyXEc+ALw==", "path": "microsoft.aspnetcore.mvc.abstractions/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-qu2EOWIqz/KFw2WV0IDltHLoKjfWr60mWl9waPJwuwpjwycaDimu8fjOEigY941tMZoWjv/ZUi2kQGKHov10/g==", "path": "microsoft.aspnetcore.mvc.core/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.core.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-ume8mo6v/hCk2OmkYP45Au5rg+FUYCpSWSbDQGHlAo4NLspHa6MB+D4INiiEzvTXC4d738E4DzkdaKc7+PYcAQ==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-a3tyBmMy1onYZbDHrbJ7nuE4xEQUSdD76T2KlE68s7xtANhIdbC/mW1FGTEZKzXawBygOaVVS7A1OzIiduxjUw==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-UwFP+BjvqzF5V9NVga0kLb4oS5LceNkYPFtMvd9imezn0+/vHKSxiIp0cWvHuksNGMld/9JjSH2KQMt0i3zkzA==", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.1.3", "hashPath": "microsoft.aspnetcore.mvc.viewfeatures.2.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sTJvhc408h4J8ml66gfhuN/r2WfrasvgERq2ZLIDz3YZYqSXmkpwDjbxSlhzuHQFKMlyx1Tg1uWoF+6eRrKjDA==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-U39z3M0oTrquVBohK32Nh20PWQkb9fuO1dbVPTI43Dr3n6qCx6vAFNGWuCzFeINLy152LivmVlLn4rMOzWudug==", "path": "microsoft.aspnetcore.routing/2.1.1", "hashPath": "microsoft.aspnetcore.routing.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Aa88Bi0/HI8dPReC0XqByPiVGYDRfj6Xh2eVsNCisnlgFHonDdW9CQsNPhVSK+uWQl3kDMFxFpeJ1ktz/wUHsQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PGKIZt4+412Z/XPoSjvYu/QIbTxcAQuEFNoA1Pw8a9mgmO0ZhNBmfaNyhgXFf7Rq62kP0tT/2WXpxdcQhkFUPA==", "path": "microsoft.aspnetcore.webutilities/2.1.1", "hashPath": "microsoft.aspnetcore.webutilities.2.1.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K63Y4hORbBcKLWH5wnKgzyn7TOfYzevIEwIedQHBIkmkEBA9SCqgvom+XTuE+fAFGvINGkhFItaZ2dvMGdT5iw==", "path": "microsoft.bcl.asyncinterfaces/1.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-RVdgNWT/73M0eCpreGpWv5NmbHFGQzzW+G7nChK8ej84m+d1nzeWrtqcRYnEpKNx3B8V/Uek4tNP0WCaCNjYnQ==", "path": "microsoft.extensions.dependencyinjection/2.1.1", "hashPath": "microsoft.extensions.dependencyinjection.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TD5QHg98m3+QhgEV1YVoNMl5KtBw/4rjfxLHO0e/YV9bPUBDKntApP4xdrVtGgCeQZHVfC2EXIGsdpRNrr87Pg==", "path": "microsoft.extensions.dependencymodel/6.0.0", "hashPath": "microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UEQB5/QPuLYaCvScZQ9llhcks5xyEUKh41D615FoehRAF9UgGVmXHcCSOH8idHHLRoKm+OJJjEy1oywvuaL33w==", "path": "microsoft.extensions.fileproviders.abstractions/2.1.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-kVVdHnOFJbcXxgZzrT6nwkrWZTHL+47LT59S9J2Jp0BNO3EQWNEZHUUZMb/kKFV7LtW+bp+EuAOPNUqEcqI++Q==", "path": "microsoft.extensions.hosting.abstractions/2.1.1", "hashPath": "microsoft.extensions.hosting.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6v66lA0RqutBDseLtX6MAZHUcaTBk2xfhnfHpcBeLtlx7jySHg/CNociGLPW7oHJtrJ+POZ8xDEoAyQp5RbWXw==", "path": "microsoft.extensions.localization/2.1.1", "hashPath": "microsoft.extensions.localization.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-bsDw+b5BaiFej/Nei6IiJFhsOtiXdDmJCabkU45WC3DQafHOLUWuArpVar8Vv2VxHrXGkOWRA7gX31LASqcaMA==", "path": "microsoft.extensions.localization.abstractions/2.1.1", "hashPath": "microsoft.extensions.localization.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-SErON45qh4ogDp6lr6UvVmFYW0FERihW+IQ+2JyFv1PUyWktcJytFaWH5zarufJvZwhci7Rf1IyGXr9pVEadTw==", "path": "microsoft.extensions.objectpool/2.1.1", "hashPath": "microsoft.extensions.objectpool.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-XIuJXPNUAX/ZV/onarixNoq3kO7Q9/RXXOY8hhYydsDwHI9PqPeJH6WE3LmPJJDmB+7y3+MT6ZmW78gZZDApBA==", "path": "microsoft.extensions.webencoders/2.1.1", "hashPath": "microsoft.extensions.webencoders.2.1.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-gIw8Sr5ZpuzKFBTfJonh2F54DivTzm5IIK15QB4Y6uE30uQdEO1NnCojTC/b6sWZoZzD0sdBa6SqwMXhucD+nA==", "path": "microsoft.identitymodel.abstractions/7.3.1", "hashPath": "microsoft.identitymodel.abstractions.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-mXA6AoaD5uZqtsKghgRiupBhyXNii8p9F2BjNLnDGud0tZLS5+4Fio2YAGjFXhnkc80CqgQ61X5U1gUNnDEoKQ==", "path": "microsoft.identitymodel.jsonwebtokens/7.3.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-uPt2aiRUCbcOc0Wk+dDCSClFfPNs3S3Z7fmy50MoxJ1mGmtVUDMpyRJeYzZ/16x4rL19T+g2zrzjcWoitp5+gQ==", "path": "microsoft.identitymodel.logging/7.3.1", "hashPath": "microsoft.identitymodel.logging.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-BA+048lSJsWecQDaRYoEAtvSxb9LKv54eku1jiWHc5Giu0FG5nNgm/pKiBheOJSmXw2ZPl048HyqotDzDU6jXg==", "path": "microsoft.identitymodel.protocols/7.3.1", "hashPath": "microsoft.identitymodel.protocols.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-TK3XOKE+7/BXciZyn71K8Csyr4zYbyo0EA/G5AUSR8wfrh3NBCwhytJeKq8QN41bJV++8xAmqesR9g/paoQ2zg==", "path": "microsoft.identitymodel.protocols.openidconnect/7.3.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.3.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-/c/p8/3CAH706c0ii5uTgSb/8M/jwyuurtdMeKTBeKFU9aA+EZrLu1M8aaS3CSlGaxoxsoaxr4/+KXykgQ4VgQ==", "path": "microsoft.identitymodel.tokens/7.3.1", "hashPath": "microsoft.identitymodel.tokens.7.3.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lP<PERSON>phl8b2EuhOE9dMH6EZDmu7pS882O+HMi5BJNsigxHaWlBrYxZHFZgE18cyaPp6SSZcTkKkuzfjV/RRQKlA==", "path": "microsoft.net.http.headers/2.1.1", "hashPath": "microsoft.net.http.headers.2.1.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ==", "path": "microsoft.netcore.platforms/2.0.0", "hashPath": "microsoft.netcore.platforms.2.0.0.nupkg.sha512"}, "Microsoft.NETCore.Runtime/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-WIblAPds88Mwvcu8OjmspmHLG9zyay//n1jMVxQlxikGzZBIeRDz/O7o9qBtOR+vDpfn+Y2EbzdCmPb3brMGRg==", "path": "microsoft.netcore.runtime/1.0.1", "hashPath": "microsoft.netcore.runtime.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EQlk4pidS+VppUSjhCCMXYlw9mf/47BwyM5XIX/gQHp5/qedKG7jypSMy0SGwv80U5mr1juQC0YROqjr7j8nTA==", "path": "microsoft.netcore.runtime.coreclr/1.0.1", "hashPath": "microsoft.netcore.runtime.coreclr.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Runtime.Native/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VeZR/qn/+FRH5rd1htnwBFIzSBW6xiA7Yu2UzaHKKlyf9Ev9xVXIOitWnkvb/tJMTKdmiCzmfi2TsAMajUHNZA==", "path": "microsoft.netcore.runtime.native/1.0.1", "hashPath": "microsoft.netcore.runtime.native.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+FWlwd//+Tt56316p00hVePBCouXyEzT86Jb3+AuRotTND0IYn0OO3obs1gnQEs/txEnt+rF2JBGLItTG+Be6A==", "path": "microsoft.win32.registry/4.5.0", "hashPath": "microsoft.win32.registry.4.5.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MiniWord/0.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-V5+1ieWLQbt4oLx0gTksyTvv/klID4UG7r1R8r/Mcfd3AbxkMe21nwUr75im7O/vp94/gi6FCxD2qleFgklHig==", "path": "miniword/0.6.1", "hashPath": "miniword.0.6.1.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5PYT/IqQ+UK31AmZiSS102R6EsTo+LGTSI8bp7WAUqDKaF4wHXD8U9u4WxTI1vc64tYi++8p3dk3WWNqPFgldw==", "path": "newtonsoft.json.bson/1.0.1", "hashPath": "newtonsoft.json.bson.1.0.1.nupkg.sha512"}, "QRCoder/1.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-W8KwCwsOJe9SI7Cm7XeIMawd08F/US6xNw34lg2Qnx6m7GYYakxbyBQaNoW1Q75oJOmX/32sJUZr6Cix2B6GUQ==", "path": "qrcoder/1.4.1", "hashPath": "qrcoder.1.4.1.nupkg.sha512"}, "ReverseMarkdown/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-16jv/gdvhmIpPxQpsdVCSAkAioXZkWX6ttxpb3ddaD+LJ4Dc+KlAhk1t21y+NZ6VMihJCw3Tum36b41N1J6cvA==", "path": "reversemarkdown/4.4.0", "hashPath": "reversemarkdown.4.4.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-s2OZDcwaQJFGfYDB1JiYCFAG4ZcFfqQAz6NWKEoj+1bIJaDJ1NqE4+H54T6qO1AtY2j1KzjXL9P/7ZVaEIiIpQ==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-L9yy+7wSNkte86DMXRuMp+/5oFvotIDIQCE8RpyyBiKPO9SbzJRMzTuyyG1MC6p8OM0tnFQZQZ+RsmlIIr+Msw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-aEv8q9AYTpFFFSxGIvORryVnmaVQv3Wqza9zz65sfzttkBRLjiA4f3sR4kmi5PYuuJ+aJpJ3UGK5e8Jme3oC+w==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ago1oEpnE0/UiqyZ+W/GxZ98EWY+F7kOQlpb7oE6fbh0fLjHb9oeftBXvyecPa6fZbk8h4aRKuchbMfb/dPuiA==", "path": "runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-UPrVPlqPRSVZaB4ADmbsQ77KXn9ORiWXyA1RP2W2+byCh3bhgT1bQz0jbeOoog9/2oTQ5wWZSDSMeb74MjezcA==", "path": "runtime.native.system.security.cryptography.apple/4.3.1", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.1.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AVFF9TO1d20IkWvmvD5qT57y/uANI6Z37Bq/4MII3kiY9ASErUgWzLJq0bGE9X++VmHQbd0alVX/xIb1L5/OkQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-u/fmZ21uI3ltvLtEMvZ6dwxes2QsPbjdR33WsjOXR/W26TMwkkVC8EkRTKTqiC51y4V1O63r4nCWoGxb53hkwg==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubUNxlB7GMmokPVmjrMiUcuX+kp71tz8dhnFDJw/ZZ8kUzh+MLFxcxp0w5HybFxF8Kg2ITCI2xbU5dgdj6tc2A==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-t15yGf5r6vMV1rB5O6TgfXKChtCaN3niwFw44M2ImX3eZ8yzueplqMqXPCbWzoBDHJVz9fE+9LFUGCsUmS2Jgg==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.1", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.1.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtBgiuJQebcbxuq+c0U5obodXhZsKHbKbG5OE34FT8qy3kytaGC1zIukAdy34xis1DGV8ILCuJ1p+2WJbCoohg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZrPDyuZGOsP6q+JKsy/jJsT7FEwK5izb1vF5ORMdlcaX0to/MHu+geEQ2+/nSXGY0mNPUSESI2L6Z+1N1TKTlQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHDwxA21BaamwTyNxN7EJQEvzLbl71YPZ7Gsxri4sRp7LgQ1v3pwN+rCmW7BhAoiWWMA3sPrXKHtYVr7L7/KTw==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1wWRUd17zhxnMpn/zShQvKG5tn4FM/DOEiMyqXLcJq/txxhQ/sXrd4RSeeimTyWmhnmg4ec7ucVH574eOGN/5w==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "SimpleHelpers.MemoryCache/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-9NwVVkUZtUqKz5BIYFNxNxHz4vEyIblUpQ6YYtEt4hafGxv4KuQNuJYxQf162vsSHVf/Gf+Erf4Xqi1MWqXm7A==", "path": "simplehelpers.memorycache/1.1.1", "hashPath": "simplehelpers.memorycache.1.1.1.nupkg.sha512"}, "SimpleHelpers.NamedLock/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-nXXGuIKFyFNyljp9B58SV9rzaBmKTYVYKL8R/SyPmLiTo4I2YpDHski+VDZTxT3Mn7l6Zql2dG84A3rka/RNRg==", "path": "simplehelpers.namedlock/1.0.3", "hashPath": "simplehelpers.namedlock.1.0.3.nupkg.sha512"}, "SkiaSharp/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "path": "skiasharp/2.88.6", "hashPath": "skiasharp.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.80.3": {"type": "package", "serviceable": true, "sha512": "sha512-fMfXO2YRwceJjAN+gsUopmwsPQqW3iiBmnF+ZRMDOAgZRQM47Eih7acExqYkSVv8qoqR9/24Q6RjZW2ScF7k4Q==", "path": "skiasharp.nativeassets.linux.nodependencies/2.80.3", "hashPath": "skiasharp.nativeassets.linux.nodependencies.2.80.3.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "path": "skiasharp.nativeassets.macos/2.88.6", "hashPath": "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "path": "skiasharp.nativeassets.win32/2.88.6", "hashPath": "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "path": "system.configuration.configurationmanager/8.0.0", "hashPath": "system.configuration.configurationmanager.8.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-iE8biOWyAC1NnYcZGcgXErNACvIQ6Gcmg5s28gsjVbyyYdF9NdKsYzAPAsO3KGK86EQjpToI1AO82XbG8chkzA==", "path": "system.identitymodel.tokens.jwt/7.3.1", "hashPath": "system.identitymodel.tokens.jwt.7.3.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9VV4KAbgRQZ79iEoG40KIeZy38O30oWwewScAST879+oki8g/Wa2HXZQgrhDDxQM4GkP1PnRJll05NMiVPbYAw==", "path": "system.io.packaging/4.7.0", "hashPath": "system.io.packaging.4.7.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-DVUblnRfnarrI5olEC2B/OCsJQd0anjVaObQMndHSc43efbc88/RMOlDyg/EyY0ix5ecyZMXS8zMksb5ukebZA==", "path": "system.security.cryptography.algorithms/4.3.1", "hashPath": "system.security.cryptography.algorithms.4.3.1.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-LGbXi1oUJ9QgCNGXRO9ndzBL/GZgANcsURpMhNR8uO+rca47SZmciS3RSQUvlQRwK3QHZSHNOXzoMUASKA+Anw==", "path": "system.security.cryptography.pkcs/6.0.4", "hashPath": "system.security.cryptography.pkcs.6.0.4.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-puJ4UCh9JVRwOCyCIcq71JY6Axr8Sp8E2GjTIU1Fj8hm4+oX6NEoyGFGa/+pBG8SrVxbQPSj7hvtaREyTHHsmw==", "path": "system.security.cryptography.xml/6.0.0", "hashPath": "system.security.cryptography.xml.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "path": "system.security.principal.windows/4.5.0", "hashPath": "system.security.principal.windows.4.5.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "path": "system.text.encoding.codepages/7.0.0", "hashPath": "system.text.encoding.codepages.7.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-WSKUTtLhPR8gllzIWO2x6l4lmAIfbyMAiTlyXAis4QBDonXK4b4S6F8zGARX4/P8wH3DH+sLdhamCiHn+fTU1A==", "path": "system.threading.tasks.extensions/4.5.1", "hashPath": "system.threading.tasks.extensions.4.5.1.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "TMS.FlexCel/7.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-B3J0LfVNP8q6nGXmhaVxRZEsXi8SsPJUR287WuBb6DHKsJ3tJaM/CQFn9RbJXdJvw75fW+ILL/JqNKlUBn55GA==", "path": "tms.flexcel/7.6.4", "hashPath": "tms.flexcel.7.6.4.nupkg.sha512"}, "Aspose.Words/20.6.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Aspose.Words.Pdf2Word/20.6.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}