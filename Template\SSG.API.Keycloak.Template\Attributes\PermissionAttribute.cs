﻿using System;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SSG.API.Keycloak.Template.Extensions;

namespace SSG.API.Keycloak.Template.Attributes
{
    public class PermissionAttribute : Attribute, IAuthorizationFilter
    {
        public string Resource { get; set; }
        public string Scope { get; set; }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            if(context.HttpContext.User.IsInRole("admin")) return;

            var lstPermission = context.HttpContext.User.GetPermissions();
            if (lstPermission == null)
            {
                context.Result = new ForbidResult();
                return;
            }

            if (!lstPermission.Any(e => e.ResourceName.Equals(Resource) && e.Scopes.Contains(Scope)))
            {
                context.Result = new ForbidResult();
                return;
            }
        }
    }
}
