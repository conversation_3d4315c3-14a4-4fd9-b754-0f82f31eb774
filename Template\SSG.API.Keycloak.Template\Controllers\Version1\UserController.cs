﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SSG.API.Keycloak.Template.Extensions;
using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces.Api;

namespace SSG.API.Keycloak.Template.Controllers.Version1
{
    /// <summary>
    /// 
    /// </summary>
    [Authorize]
    [Route("api/v1/[controller]/[action]")]
    [ApiController]
    public class UserController : SSGController
    {
        private readonly IConfiguration _configuration;
        private readonly IApiProfileService _apiProfileService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="apiProfileService"></param>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        public UserController(IApiProfileService apiProfileService, IConfiguration configuration, ILoggerFactory logger) : base(logger)
        {
            _configuration = configuration;
            _apiProfileService = apiProfileService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<Authorization.UserToken> GetUserProfile()
        {
            try
            {
                var userToken = new Authorization.UserToken
                {
                    UserId = User.GetUserId(),
                    UserName = User.GetUserName(),
                    Roles = User.GetRoles(),
                    Permissions = User.GetPermissions(Request.GetAccessToken())
                };

                var userProfile = await _apiProfileService.GetUserProfile(Request.GetAccessToken());
                userToken.Profile = userProfile.data;

                return userToken;
            }
            catch (Exception ex)
            {
                _logger.LogError($"{ex}");
                throw;
            }
        }
    }
}
