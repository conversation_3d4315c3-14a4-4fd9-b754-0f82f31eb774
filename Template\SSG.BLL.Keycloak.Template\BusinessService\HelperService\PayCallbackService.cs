﻿using SSG.Model;
using SSG.Model.SPay;
using SSG.Utils;

namespace SSG.BLL.Keycloak.Template.BusinessService.HelperService
{
    public class PayCallbackService : HttpClientBase
    {
        public string SetPayCallback(WalPayCallback data, string callbackUrl)
        {
            var url = callbackUrl;
            var response = Post<WalPayCallback, BaseResponse<string>>(url, data);
            
            if (response.Item1 == null)
                return null;
            else
                return response.Item1?.Data;
        }
    }
}
