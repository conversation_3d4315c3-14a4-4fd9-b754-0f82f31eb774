﻿using Dapper;
using Microsoft.Extensions.Configuration;
using SSG.DAL.Keycloak.Template.Interfaces;
using SSG.Model;
using SSG.Model.Api;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace SSG.DAL.Keycloak.Template.Repositories
{
    public class ClassRepository : IClassRepository
    {
        private readonly string _connectionString;

        public ClassRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("Yamaha_Training");
        }

        //public async Task<YmClassPage> GetPageAsync(FilterBase query)
        //{
        //    const string storedProcedure = "sp_YAM_Class_page";
        //    using (var connection = new SqlConnection(_connectionString))
        //    {
        //        await connection.OpenAsync();
        //        var param = new DynamicParameters();
        //        param.Add("@userId", query.userId);
        //        param.Add("@filter", query.filter);
        //        param.Add("@Offset", query.offSet);
        //        param.Add("@PageSize", query.pageSize);
        //        param.Add("@Total", 0, DbType.Int64, ParameterDirection.InputOutput);
        //        param.Add("@TotalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);
        //        var dataMulti = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

        //        var data = new YmClassPage();

        //        if (query.offSet == null || query.offSet == 0)
        //        {
        //            data.gridflexs = dataMulti.Read<viewGridFlex>().ToList();
        //        }

        //        var dataList = dataMulti.Read<object>().ToList();
        //        data.dataList = new ResponseList<List<object>>(dataList, param.Get<long>("@Total"), param.Get<long>("@TotalFiltered"));

        //        return data;
        //    }
        //}

        //public async Task<YmClassInfo> GetInfoAsync(string userId, Guid? id)
        //{
        //    const string storedProcedure = "sp_YAM_Class_fields";
        //    using (var connection = new SqlConnection(_connectionString))
        //    {
        //        connection.Open();
        //        var param = new DynamicParameters();
        //        param.Add("@userId", userId);
        //        param.Add("@id", id);
        //        var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //        var data = result.Read<YmClassInfo>().FirstOrDefault();
        //        if (data == null) return data;
        //        data.group_fields = result.Read<viewGroup>().ToList();
        //        if (data.group_fields == null || data.group_fields.Count <= 0) return data;
        //        var fields = result.Read<viewField>().ToList();
        //        foreach (var gr in data.group_fields)
        //        {
        //            gr.fields = fields.Where(f => f.group_cd == gr.group_cd).ToList();
        //        }
        //        return data;
        //    }
        //}

        //public async Task<BaseValidate> SetInfoAsync(string userId, YmClassInfo info)
        //{
        //    const string storedProcedure = "sp_YAM_Class_set";
        //    using (var connection = new SqlConnection(_connectionString))
        //    {
        //        connection.Open();
        //        var param = new DynamicParameters();
        //        param.Add("@userId", userId);
        //        param.Add("@id", info.Id);
        //        param.Add("@code", info.GetValueByFieldName("code"));
        //        param.Add("@name", info.GetValueByFieldName("name"));
        //        param.Add("@en_name", info.GetValueByFieldName("en_name"));
        //        param.Add("@shortName", info.GetValueByFieldName("shortName"));
        //        param.Add("@CourseId", info.GetValueByFieldName("CourseId"));
        //        param.Add("@TrainingTypeId", info.GetValueByFieldName("TrainingTypeId"));
        //        param.Add("@Teacher", info.GetValueByFieldName("Teacher"));
        //        param.Add("@TrainingPlace", info.GetValueByFieldName("TrainingPlace"));
        //        param.Add("@TrainerInfo", info.GetValueByFieldName("TrainerInfo"));
        //        param.Add("@TrainingAgent", info.GetValueByFieldName("TrainingAgent"));
        //        param.Add("@StartDate", info.GetValueByFieldName("StartDate"));
        //        param.Add("@EndDate", info.GetValueByFieldName("EndDate"));
        //        param.Add("@NumberOfStudent", info.GetValueByFieldName("NumberOfStudent"));
        //        param.Add("@Duration", info.GetValueByFieldName("Duration"));
        //        //param.Add("@AvgPoint", info.GetValueByFieldName("AvgPoint"));
        //        param.Add("@CostByTrainee", info.GetValueByFieldName("CostByTrainee"));
        //        param.Add("@CostByClass", info.GetValueByFieldName("CostByClass"));
        //        var result = await connection.QueryFirstOrDefaultAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
        //        return result;
        //    }
        //}

        public async Task<BaseValidate> DeleteAsync(string userId, Guid? id)
        {
            const string storedProcedure = "sp_YAM_Class_del";
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", id);
                var result = await connection.QueryFirstOrDefaultAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        //public async Task<BaseResponse<ImportClassResponse>> Import(string userId, string xml)
        //{
        //    const string storedProcedure = "sp_YAM_Class_Import";
        //    try
        //    {
        //        using (var connection = new SqlConnection(_connectionString))
        //        {
        //            connection.Open();
        //            var param = new DynamicParameters();
        //            param.Add("@userId", userId);
        //            param.Add("@xml", xml);

        //            var dataMulti = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);

        //            var data = new ImportClassResponse();
        //            data.data = dataMulti.Read<ClassImport>().ToList();
        //            var result = new BaseResponse<ImportClassResponse>(data);
        //            return result;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        var result = new BaseResponse<ImportClassResponse>();
        //        result.AddError(ApiResult.Error, ex.Message);
        //        result.SetStatus(ApiResult.Error);
        //        return result;
        //    }
        //}
    }
}
