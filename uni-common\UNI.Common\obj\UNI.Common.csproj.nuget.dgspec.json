{"format": 1, "restore": {"D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Common\\UNI.Common.csproj": {}}, "projects": {"D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Common\\UNI.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Common\\UNI.Common.csproj", "projectName": "UNI.Common", "projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Common\\UNI.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Model\\UNI.Model.csproj": {"projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Model\\UNI.Model.csproj"}, "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\UNI.Utils.csproj": {"projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\UNI.Utils.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Elastic.Apm": {"target": "Package", "version": "[1.25.3, )"}, "Google.Cloud.Firestore": {"target": "Package", "version": "[3.8.0, )"}, "Google.Cloud.PubSub.V1": {"target": "Package", "version": "[3.9.0, )"}, "Google.Cloud.Storage.V1": {"target": "Package", "version": "[4.7.0, )"}, "Microsoft.AspNetCore.DataProtection": {"target": "Package", "version": "[6.0.25, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.1.34, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.1.38, )"}, "Microsoft.AspNetCore.Mvc.ViewFeatures": {"target": "Package", "version": "[2.1.3, )"}, "Microsoft.AspNetCore.StaticFiles": {"target": "Package", "version": "[2.1.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Xml": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.IO.RecyclableMemoryStream": {"target": "Package", "version": "[2.3.2, )"}, "Microsoft.IdentityModel.Protocols": {"target": "Package", "version": "[7.3.1, )"}, "NSwag.Generation": {"target": "Package", "version": "[13.6.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[106.15.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[4.7.0, )"}, "System.ComponentModel.Primitives": {"target": "Package", "version": "[4.3.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "System.IO.FileSystem": {"target": "Package", "version": "[4.3.0, )"}, "System.Security.Cryptography.Algorithms": {"target": "Package", "version": "[4.3.1, )"}, "System.Xml.XDocument": {"target": "Package", "version": "[4.3.0, )"}, "System.Xml.XmlDocument": {"target": "Package", "version": "[4.3.0, )"}, "System.Xml.XmlSerializer": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}, "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Model\\UNI.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Model\\UNI.Model.csproj", "projectName": "UNI.Model", "projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Model\\UNI.Model.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Model\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\UNI.Utils.csproj": {"projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\UNI.Utils.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.0.35, )"}, "Dapper.ParameterExtensions": {"target": "Package", "version": "[2018.12.7.1, )"}, "ExpressiveAnnotationsCore.dll": {"target": "Package", "version": "[0.1.0, )"}, "Google.Cloud.Firestore": {"target": "Package", "version": "[3.8.0, )"}, "NJsonSchema": {"target": "Package", "version": "[10.1.23, )"}, "NSwag.Annotations": {"target": "Package", "version": "[13.10.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[4.7.0, )"}, "System.ComponentModel.Primitives": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}, "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\UNI.Utils.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\UNI.Utils.csproj", "projectName": "UNI.Utils", "projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\UNI.Utils.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utils\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Aspose.Cells": {"target": "Package", "version": "[24.1.0, )"}, "Aspose.PDF": {"target": "Package", "version": "[24.1.0, )"}, "FreeSpire.Doc": {"target": "Package", "version": "[12.2.0, )"}, "Google.Apis.Docs.v1": {"target": "Package", "version": "[1.51.0.2252, )"}, "Google.Apis.Drive.v3": {"target": "Package", "version": "[1.51.0.2265, )"}, "Google.Cloud.Storage.V1": {"target": "Package", "version": "[3.4.0, )"}, "Markdig": {"target": "Package", "version": "[0.37.0, )"}, "Microsoft.AspNetCore.Mvc.ViewFeatures": {"target": "Package", "version": "[2.1.3, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"target": "Package", "version": "[7.3.1, )"}, "Microsoft.NETCore.Runtime": {"target": "Package", "version": "[1.0.1, )"}, "MiniWord": {"target": "Package", "version": "[0.6.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "QRCoder": {"target": "Package", "version": "[1.4.1, )"}, "ReverseMarkdown": {"target": "Package", "version": "[4.4.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "SimpleHelpers.MemoryCache": {"target": "Package", "version": "[1.1.1, )"}, "SkiaSharp.NativeAssets.Linux.NoDependencies": {"target": "Package", "version": "[2.80.3, )"}, "System.ComponentModel.Primitives": {"target": "Package", "version": "[4.3.0, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[8.0.0, )"}, "System.Linq": {"target": "Package", "version": "[4.3.0, )"}, "System.Reflection.TypeExtensions": {"target": "Package", "version": "[4.7.0, )"}, "System.Security.Cryptography.Algorithms": {"target": "Package", "version": "[4.3.1, )"}, "System.Security.Cryptography.Encoding": {"target": "Package", "version": "[4.3.0, )"}, "System.Xml.ReaderWriter": {"target": "Package", "version": "[4.3.0, )"}, "System.Xml.XmlDocument": {"target": "Package", "version": "[4.3.0, )"}, "TMS.FlexCel": {"target": "Package", "version": "[7.6.4, )"}, "runtime.native.System.Security.Cryptography": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}