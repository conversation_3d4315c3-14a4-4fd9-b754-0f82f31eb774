﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Docs.v1;
using Google.Apis.Drive.v3;
using Google.Apis.Drive.v3.Data;
using Google.Apis.Services;
using Microsoft.AspNetCore.Http;
using SSG.Model.SHome;

//using SSG.Model.SHousing;

namespace SSG.BLL.Keycloak.Template.BusinessService.HelperService
{
    public class GoogleDriverBaseService
    {
        private string ApplicationName = "ssg-api";
        private string documentWriterCred = "sunshine-super-app-1032f6b27d75-document-writer.json";
        private string[] ScopesDrive = { DriveService.Scope.Drive };
        private string[] ScopesDocs = { DocsService.Scope.Documents };
        protected readonly DriveService service;
        public GoogleDriverBaseService()
        {
            string credentialPath = Path.GetFullPath(documentWriterCred);
            string[] Scopes = { DriveService.Scope.Drive, DocsService.Scope.Documents };

            ServiceAccountCredential credentials = GoogleCredential.FromFile(credentialPath).CreateScoped(Scopes).UnderlyingCredential as ServiceAccountCredential;

            if (credentials.RequestAccessTokenAsync(CancellationToken.None).Result)
            {
                service = new DriveService(
                new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credentials,
                    ApplicationName = ApplicationName
                }
            );
                //return service;
            }
            //return null;
        }
        #region private-service-doc 
        public IList<Google.Apis.Drive.v3.Data.File> ListEntities(string id = "root")
        {
            FilesResource.ListRequest listRequest = service.Files.List();
            listRequest.PageSize = 100;
            listRequest.Fields = "nextPageToken, files(id, name, parents, createdTime, modifiedTime, mimeType)";
            listRequest.Q = $"'{id}' in parents";

            return listRequest.Execute().Files;
        }
        public Google.Apis.Drive.v3.Data.File CreateFolder(string name, string id = "root")
        {
            var fileMetadata = new Google.Apis.Drive.v3.Data.File()
            {
                Name = name,
                MimeType = "application/vnd.google-apps.folder",
                Parents = new[] { id }
            };

            var request = service.Files.Create(fileMetadata);
            request.Fields = "id, name, parents, createdTime, modifiedTime, mimeType";

            return request.Execute();
        }
        public async Task<Google.Apis.Drive.v3.Data.File> Upload(IFormFile file, string documentId)
        {
            var name = ($"{DateTime.UtcNow.ToString()}.{Path.GetExtension(file.FileName)}");
            var mimeType = file.ContentType;

            var fileMetadata = new Google.Apis.Drive.v3.Data.File()
            {
                Name = name,
                MimeType = mimeType,
                Parents = new[] { documentId }
            };

            FilesResource.CreateMediaUpload request;
            using (var stream = file.OpenReadStream())
            {
                request = service.Files.Create(
                    fileMetadata, stream, mimeType);
                request.Fields = "id, name, parents, createdTime, modifiedTime, mimeType, thumbnailLink";
                await request.UploadAsync();
            }


            return request.ResponseBody;
        }
        public void Rename(string name, string id)
        {
            Google.Apis.Drive.v3.Data.File file = service.Files.Get(id).Execute();

            var update = new Google.Apis.Drive.v3.Data.File();
            update.Name = name;

            service.Files.Update(update, id).Execute();
        }
        public void Remove(string id)
        {
            service.Files.Delete(id).Execute();
        }
        public async Task<Stream> Download(string fileId)
        {
            Stream outputstream = new MemoryStream();
            var request = service.Files.Get(fileId);

            await request.DownloadAsync(outputstream);

            outputstream.Position = 0;

            return outputstream;
        }
        public string CheckExistedFolderAndCreate(string folderName, string parent,string temdriverId)
        {
            string foldeId = String.Empty;
            string pageToken = null;
            string query = "mimeType='application/vnd.google-apps.folder' and trashed = false and '" + parent + "' in parents and name = '" + folderName + "'";
            try
            {

                var result = service.Files.List();
                result.PageSize = 100;
                result.Q = query;
                result.Corpora = "teamDrive";
                result.TeamDriveId = temdriverId;
                result.SupportsTeamDrives = true;
                result.IncludeTeamDriveItems = true;
                result.Fields = "nextPageToken, files(id, name,parents,mimeType)";

                result.PageToken = pageToken;
                FileList files = result.Execute();
                if (files.Files.Count > 0)
                    foldeId = files.Files[0].Id;
                else
                {
                    var fileMetadata = new Google.Apis.Drive.v3.Data.File()
                    {
                        Name = folderName,
                        MimeType = "application/vnd.google-apps.folder",
                        Parents = new[] { parent }
                    };

                    var request = service.Files.Create(fileMetadata);
                    request.Fields = "id, name, parents, createdTime, modifiedTime, mimeType";
                    request.SupportsTeamDrives = true;
                    Google.Apis.Drive.v3.Data.File file = request.Execute();
                    if (file != null)
                        foldeId = file.Id;
                }
                return foldeId;
            }
            catch (Exception ex)
            {
                return foldeId;
            }


        }
        public Google.Apis.Drive.v3.Data.File UploadFromStream(Stream stream, string name, string mimeType, string parentId)
        {
            var fileMetadata = new Google.Apis.Drive.v3.Data.File()
            {
                Name = name,
                MimeType = mimeType,
                Parents = new List<string> { parentId },
                
            };
            FilesResource.CreateMediaUpload request = service.Files.Create(
                    fileMetadata, stream, mimeType);
            request.SupportsTeamDrives = true;
            request.Fields = "id, name, parents, createdTime, modifiedTime, mimeType, thumbnailLink,webViewLink, webContentLink";
            request.Upload();
            return request.ResponseBody;
        }


        public async Task<GoogleResultLink> GetGoogleResultLink(GoogleResultLink resultLink, string documentId, string viewerUrl)
        {
            try
            {
                //share result file
                Google.Apis.Drive.v3.Data.Permission filePermission = new Google.Apis.Drive.v3.Data.Permission();
                filePermission.Role = "writer";
                filePermission.Type = "anyone";
                Google.Apis.Drive.v3.PermissionsResource.CreateRequest permissionRequest = service.Permissions.Create(filePermission, documentId);
                permissionRequest.SupportsAllDrives = true;
                permissionRequest.SupportsTeamDrives = true;
                await permissionRequest.ExecuteAsync();

                FilesResource.GetRequest getRequest = service.Files.Get(documentId);
                getRequest.SupportsAllDrives = true;
                getRequest.SupportsTeamDrives = true;
                getRequest.Fields = "id, name, webViewLink, webContentLink";
                Google.Apis.Drive.v3.Data.File file = await getRequest.ExecuteAsync();

                resultLink.WebViewLink = file.WebContentLink; // string.Concat(viewerUrl, "/reporting_word_viewer/?fileId=", documentId);
                resultLink.PDFDownloadLink = file.WebContentLink;
                resultLink.MSWordDownloadLink = file.WebViewLink.Replace("/edit?usp=drivesdk", "/export?format=doc");
                return resultLink;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return resultLink;
        }


        #endregion private-service-doc
    }
}
