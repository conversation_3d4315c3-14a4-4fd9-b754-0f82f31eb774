﻿using FastMember;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace SSG.BLL.Keycloak.Template.BusinessService.HelperService
{
    public class DynamicColumnReport
    {
        #region BC14
        //public DataSet CreateDataReportCourseYear(YmReportBC14 filter,DataSet input)
        //{
        //    DataSet dataSet = new DataSet();
        //    DataTable groupTrainee = new DataTable();
        //    DataTable childTrainee = new DataTable();
        //    DataTable groupTraineeTemp = new DataTable();
        //    DataTable childTraineeTemp = new DataTable();
        //    List<string> yearsFix = new List<string>() { "2010", "2011","2012","2013","2014","2015","2016","2017","2018","2019","2020",
        //                                                 "2021","2022","2023","2024","2025","2026","2027","2028","2029","2030",
        //                                                 "2031","2032","2033","2034","2035","2036","2037","2038","2039","2040" };
        //    int totalColumn = yearsFix.Count();
        //    // lay du lieu

        //    if (input.Tables.Count > 0)
        //    {
        //        groupTrainee = input.Tables[0];
        //        childTrainee = input.Tables[1];

        //    }

        //    //tao column
        //    childTraineeTemp.Columns.Add("installNum", typeof(string));
        //    childTraineeTemp.Columns.Add("objectCode", typeof(string));
        //    childTraineeTemp.Columns.Add("objects", typeof(string));
        //    childTraineeTemp.Columns.Add("courceCode", typeof(string));
        //    childTraineeTemp.Columns.Add("trainingCource", typeof(string));
        //    //childTraineeTemp.Columns.Add("traineeTypeCode", typeof(string));
        //    //childTraineeTemp.Columns.Add("color", typeof(string));
        //    for (int i = 1; i <= totalColumn; i++)
        //    {
        //        childTraineeTemp.Columns.Add("Nam" + i, typeof(string));
        //        childTraineeTemp.Columns.Add("color" + i, typeof(string));
        //    }
        //    //lay du lieu dua vao list
        //    var query = (from a in childTrainee.AsEnumerable()
        //                 select new
        //                 {
        //                     objectCode = a["objectCode"],
        //                     objects = a["objects"],
        //                     courceCode = a["courceCode"],
        //                     trainingCource = a["trainingCource"],
        //                     //traineeTypeCode = a["traineeTypeCode"],
        //                     color1 = childTrainee.Columns.Contains("2010") ? RemoveDataByT(RemoveDataByT(a["2010"].ToString().Replace("x_",""),"T3"),"T1"):"",
        //                     color2 = childTrainee.Columns.Contains("2011") ? RemoveDataByT(RemoveDataByT(a["2011"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color3 = childTrainee.Columns.Contains("2012") ? RemoveDataByT(RemoveDataByT(a["2012"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color4 = childTrainee.Columns.Contains("2013") ? RemoveDataByT(RemoveDataByT(a["2013"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color5 = childTrainee.Columns.Contains("2014") ? RemoveDataByT(RemoveDataByT(a["2014"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color6 = childTrainee.Columns.Contains("2015") ? RemoveDataByT(RemoveDataByT(a["2015"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color7 = childTrainee.Columns.Contains("2016") ? RemoveDataByT(RemoveDataByT(a["2016"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color8 = childTrainee.Columns.Contains("2017") ? RemoveDataByT(RemoveDataByT(a["2017"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color9 = childTrainee.Columns.Contains("2018") ? RemoveDataByT(RemoveDataByT(a["2018"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color10 = childTrainee.Columns.Contains("2019") ? RemoveDataByT(RemoveDataByT(a["2019"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color11 = childTrainee.Columns.Contains("2020") ? RemoveDataByT(RemoveDataByT(a["2020"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color12 = childTrainee.Columns.Contains("2021") ? RemoveDataByT(RemoveDataByT(a["2021"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color13 = childTrainee.Columns.Contains("2022") ? RemoveDataByT(RemoveDataByT(a["2022"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color14 = childTrainee.Columns.Contains("2023") ? RemoveDataByT(RemoveDataByT(a["2023"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color15 = childTrainee.Columns.Contains("2024") ? RemoveDataByT(RemoveDataByT(a["2024"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color16 = childTrainee.Columns.Contains("2025") ? RemoveDataByT(RemoveDataByT(a["2025"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color17 = childTrainee.Columns.Contains("2026") ? RemoveDataByT(RemoveDataByT(a["2026"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color18 = childTrainee.Columns.Contains("2027") ? RemoveDataByT(RemoveDataByT(a["2027"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color19 = childTrainee.Columns.Contains("2028") ? RemoveDataByT(RemoveDataByT(a["2028"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color20 = childTrainee.Columns.Contains("2029") ? RemoveDataByT(RemoveDataByT(a["2029"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color21 = childTrainee.Columns.Contains("2030") ? RemoveDataByT(RemoveDataByT(a["2030"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color22 = childTrainee.Columns.Contains("2031") ? RemoveDataByT(RemoveDataByT(a["2031"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color23 = childTrainee.Columns.Contains("2032") ? RemoveDataByT(RemoveDataByT(a["2032"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color24 = childTrainee.Columns.Contains("2033") ? RemoveDataByT(RemoveDataByT(a["2033"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color25 = childTrainee.Columns.Contains("2034") ? RemoveDataByT(RemoveDataByT(a["2034"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color26 = childTrainee.Columns.Contains("2035") ? RemoveDataByT(RemoveDataByT(a["2035"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color27 = childTrainee.Columns.Contains("2036") ? RemoveDataByT(RemoveDataByT(a["2036"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color28 = childTrainee.Columns.Contains("2037") ? RemoveDataByT(RemoveDataByT(a["2037"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color29 = childTrainee.Columns.Contains("2038") ? RemoveDataByT(RemoveDataByT(a["2038"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color30 = childTrainee.Columns.Contains("2039") ? RemoveDataByT(RemoveDataByT(a["2039"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     color31 = childTrainee.Columns.Contains("2040") ? RemoveDataByT(RemoveDataByT(a["2040"].ToString().Replace("x_", ""), "T3"), "T1") : "",
        //                     //color = a["color"],
        //                     Nam1 = childTrainee.Columns.Contains("2010") ? (a["2010"].ToString().StartsWith("x_")? "x" : ""):"0",
        //                     Nam2 = childTrainee.Columns.Contains("2011") ? (a["2011"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam3 = childTrainee.Columns.Contains("2012") ? (a["2012"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam4 = childTrainee.Columns.Contains("2013") ? (a["2013"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam5 = childTrainee.Columns.Contains("2014") ? (a["2014"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam6 = childTrainee.Columns.Contains("2015") ? (a["2015"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam7 = childTrainee.Columns.Contains("2016") ? (a["2016"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam8 = childTrainee.Columns.Contains("2017") ? (a["2017"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam9 = childTrainee.Columns.Contains("2018") ? (a["2018"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam10 = childTrainee.Columns.Contains("2019") ? (a["2019"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam11 = childTrainee.Columns.Contains("2020") ? (a["2020"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam12 = childTrainee.Columns.Contains("2021") ? (a["2021"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam13 = childTrainee.Columns.Contains("2022") ? (a["2022"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam14 = childTrainee.Columns.Contains("2023") ? (a["2023"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam15 = childTrainee.Columns.Contains("2024") ? (a["2024"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam16 = childTrainee.Columns.Contains("2025") ? (a["2025"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam17 = childTrainee.Columns.Contains("2026") ? (a["2026"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam18 = childTrainee.Columns.Contains("2027") ? (a["2027"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam19 = childTrainee.Columns.Contains("2028") ? (a["2028"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam20 = childTrainee.Columns.Contains("2029") ? (a["2029"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam21 = childTrainee.Columns.Contains("2030") ? (a["2030"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam22 = childTrainee.Columns.Contains("2031") ? (a["2031"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam23 = childTrainee.Columns.Contains("2032") ? (a["2032"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam24 = childTrainee.Columns.Contains("2033") ? (a["2033"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam25 = childTrainee.Columns.Contains("2034") ? (a["2034"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam26 = childTrainee.Columns.Contains("2035") ? (a["2035"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam27 = childTrainee.Columns.Contains("2036") ? (a["2036"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam28 = childTrainee.Columns.Contains("2037") ? (a["2037"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam29 = childTrainee.Columns.Contains("2038") ? (a["2038"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam30 = childTrainee.Columns.Contains("2039") ? (a["2039"].ToString().StartsWith("x_") ? "x" : "") : "0",
        //                     Nam31 = childTrainee.Columns.Contains("2040") ? (a["2040"].ToString().StartsWith("x_") ? "x" : "") : "0"
        //                 }).ToList();
        //    //dua du lieu vao bang tam
        //    if (query != null && query.Count > 0)
        //    {
        //        using (var reader = ObjectReader.Create(query))
        //        {
        //            childTraineeTemp.Load(reader);
        //        }
        //    }

        //    //merge dong co du lieu giong nhau theo cot
        //    childTraineeTemp = RemoveDuplicateRows(childTraineeTemp, "objectCode");
        //    childTraineeTemp = RemoveDuplicateRows(childTraineeTemp, "objects");

        //    //chen thu tu va format boder theo cell
        //    childTraineeTemp = TakeInstallNumAndBoder(childTraineeTemp, "installNum", "objects", "objectCode");
        //    dataSet.Tables.Add(childTraineeTemp);
        //    return dataSet;
        //}
        //public Dictionary<string,object> CreateDataDicReportCourceYear(YmReportBC14 filter, DataSet input)
        //{
        //    List<string> yearsFix = new List<string>() { "2010", "2011","2012","2013","2014","2015","2016","2017","2018","2019","2020",
        //                                                 "2021","2022","2023","2024","2025","2026","2027","2028","2029","2030",
        //                                                 "2031","2032","2033","2034","2035","2036","2037","2038","2039","2040" };
        //    int startYearFix = 2010;
        //    int totalColumn = yearsFix.Count();
        //    string Organizer = "";
        //    int j = 0;
        //    Dictionary<String, Object> p = new Dictionary<string, object>();
        //    if (input.Tables.Count > 0)
        //    {
        //        Organizer = input.Tables[2].Rows.Count > 0 ? input.Tables[2].Rows[0][0].ToString() : String.Empty;
        //        filter.LstYear = input.Tables[3].Rows.Count > 0 ? input.Tables[3].Rows[0][0].ToString() : DateTime.Now.Year.ToString();

        //    }
        //    p.Add("Organizer", Organizer);
        //    for (int i = 1; i <= totalColumn; i++)
        //    {
        //        p.Add("Nam" + i.ToString(), filter.LstYear.Split(",").ToList().Any(t => t.Contains((startYearFix + j).ToString())) ? (startYearFix + j).ToString() : "0");
        //        j = j + 1;
        //    }
        //    return p;
        //}
        public DataTable RemoveDuplicateRows(DataTable dTable, string colName)
        {
            Hashtable hTable = new Hashtable();
            string valueOld = String.Empty;
            List<string> duplicate = new List<string>();

            //Add list of all the unique item value to hashtable, which stores combination of key, value pair.
            //And add duplicate item value in arraylist.
            foreach (DataRow drow in dTable.Rows)
            {

                if (drow[colName].ToString() == valueOld || duplicate.Any(t => t.Contains(drow[colName].ToString())))
                {
                    drow[colName] = String.Empty;
                }
                valueOld = drow[colName].ToString();
                if (!duplicate.Any(t => t.Contains(drow[colName].ToString())))
                    duplicate.Add(drow[colName].ToString());
            }
            return dTable;
        }
        public DataTable TakeInstallNumAndBoder(DataTable dTable, string colInstall, string colObject, string colObjectCode)
        {
            int i = 1;
            foreach (DataRow drow in dTable.Rows)
            {
                if (drow[colObject].ToString() == String.Empty)
                {
                    drow[colObject] = String.Empty;
                    drow[colObjectCode] = String.Empty;
                    continue;
                }
                drow[colInstall] = i.ToString();
                i = i + 1;
            }
            return dTable;
        }
        public string RemoveDataByT(string result,string input)
        {
            return result.Trim().StartsWith(input) ? input : result;
        }
        #endregion BC14

        #region BC11
        //public DataSet CreateDataObjectTraneeProcessReport(YmrReportBC11 filter, DataSet input)
        //{
        //    DataSet dtSet = new DataSet();
        //    DataTable table = new DataTable();
        //    DataTable tableYear = new DataTable();
        //    table = input.Tables[0];
        //    tableYear = input.Tables[1];
        //    DataTable dtdata = new DataTable();
        //    dtdata.Columns.Add("Stt", typeof(string));
        //    dtdata.Columns.Add("MaNhanVien", typeof(string));
        //    dtdata.Columns.Add("Nam", typeof(string));
        //    dtdata.Columns.Add("HoTen", typeof(string));
        //    dtdata.Columns.Add("NgaySinh", typeof(string));
        //    dtdata.Columns.Add("GioiTinh", typeof(string));
        //    dtdata.Columns.Add("BoPhan", typeof(string));
        //    dtdata.Columns.Add("Phong", typeof(string));
        //    dtdata.Columns.Add("To", typeof(string));
        //    dtdata.Columns.Add("NoiLamViec", typeof(string));
        //    dtdata.Columns.Add("ChucVu", typeof(string));
        //    dtdata.Columns.Add("BoPhanToChuc", typeof(string));
        //    for (int i = 1; i <= 10; i++)
        //    {
        //        for (int j = 1; j <= 10; j++)
        //        {
        //            dtdata.Columns.Add("Nam" + i + "_NhomDoiTuongHV" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_MaKhoaHoc" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_TenKhoaHoc" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_MaLop" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_TenLopDaoTao" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_ThoiLuong" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_KetQuaDaoTao" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_DiemKiemTra" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_HinhThucDaoTao" + j, typeof(string));
        //        }
        //    }

        //    if (table.Rows.Count > 0)
        //    {
        //        var query = (from a in table.AsEnumerable()
        //                     select new
        //                     {
        //                         Stt = a["Stt"],
        //                         MaNhanVien = a["MaNhanVien"],
        //                         Nam = a["Nam"],
        //                         HoTen = a["HoTen"],
        //                         NgaySinh = a["NgaySinh"],
        //                         GioiTinh = a["GioiTinh"],
        //                         BoPhan = a["BoPhan"],
        //                         Phong = a["Phong"],
        //                         To = a["To"],
        //                         NoiLamViec = a["NoiLamViec"],
        //                         ChucVu = a["ChucVu"],
        //                         BoPhanToChuc = a["BoPhanToChuc"],
        //                         NhomDoiTuongHV = a["NhomDoiTuongHV"],
        //                         MaKhoaHoc = a["MaKhoaHoc"],
        //                         TenKhoaHoc = a["TenKhoaHoc"],
        //                         MaLop = a["MaLop"],
        //                         TenLopDaoTao = a["TenLopDaoTao"],
        //                         ThoiLuong = a["ThoiLuong"],
        //                         KetQuaDaoTao = a["KetQuaDaoTao"],
        //                         DiemKiemTra = a["DiemKiemTra"],
        //                         HinhThucDaoTao = a["HinhThucDaoTao"]

        //                     }).ToList();

        //        foreach (var item1 in query)
        //        {
        //            DataRow row = dtdata.NewRow();
        //            row["Stt"] = item1.Stt;
        //            row["MaNhanVien"] = item1.MaNhanVien;
        //            row["Nam"] = item1.Nam;
        //            row["HoTen"] = item1.HoTen;
        //            row["NgaySinh"] = item1.NgaySinh;
        //            row["GioiTinh"] = item1.GioiTinh;
        //            row["BoPhan"] = item1.BoPhan;
        //            row["Phong"] = item1.Phong;
        //            row["To"] = item1.To;
        //            row["NoiLamViec"] = item1.NoiLamViec;
        //            row["ChucVu"] = item1.ChucVu;
        //            row["BoPhanToChuc"] = item1.BoPhanToChuc;
        //            dtdata.Rows.Add(row);
        //        }
        //        RemoveDistinctRows(dtdata, "MaNhanVien");
        //        //update du lieu that
        //        int kk = 1;
        //        string nam = String.Empty;
        //        foreach (DataRow row in dtdata.Rows)
        //        {
        //            //for (int ii = 1; ii <= 10; ii++)
        //            //{
        //            string manv = row["MaNhanVien"].ToString();
        //            //ii = 1;
        //            kk = 1;
        //            var lst = query.OrderBy(t => t.MaNhanVien).OrderBy(t => t.Nam).Where(t => t.MaNhanVien.ToString() == manv).ToList();
        //            //var lsy = lst.Select(t => t.Nam).Distinct();
        //            for (int ii = 0; ii < tableYear.Rows.Count; ii++)
        //            {
        //                kk = 1;
        //                foreach (var it in lst.Where(t => t.Nam.ToString() == tableYear.Rows[ii]["year"].ToString()).ToList())
        //                {
        //                    row["Nam" + (ii + 1) + "_NhomDoiTuongHV" + kk] = it.NhomDoiTuongHV;
        //                    row["Nam" + (ii + 1) + "_MaKhoaHoc" + kk] = it.MaKhoaHoc;
        //                    row["Nam" + (ii + 1) + "_TenKhoaHoc" + kk] = it.TenKhoaHoc;
        //                    row["Nam" + (ii + 1) + "_MaLop" + kk] = it.MaLop;
        //                    row["Nam" + (ii + 1) + "_TenLopDaoTao" + kk] = it.TenLopDaoTao;
        //                    row["Nam" + (ii + 1) + "_ThoiLuong" + kk] = it.ThoiLuong;
        //                    row["Nam" + (ii + 1) + "_KetQuaDaoTao" + kk] = it.KetQuaDaoTao;
        //                    row["Nam" + (ii + 1) + "_DiemKiemTra" + kk] = it.DiemKiemTra;
        //                    row["Nam" + (ii + 1) + "_HinhThucDaoTao" + kk] = it.HinhThucDaoTao;
        //                    kk++;

        //                }
        //            }
        //        }
        //    }
        //    dtSet.Tables.Add(dtdata);
        //    return dtSet;
        //}
        //public Dictionary<string, object> CreateDataDicObjectTraneeProcess(YmrReportBC11 filter, DataSet input, string headerTitle)
        //{
        //    DataTable tableYear = new DataTable();
        //    tableYear = input.Tables[1];
        //    Dictionary<String, Object> p = new Dictionary<string, object>();
        //    for (int i = 0; i < 10; i++)
        //    {
        //        p.Add("Nam" + (i + 1), i >= tableYear.Rows.Count ? String.Empty : tableYear.Rows[i]["year"].ToString());
        //    }
        //    //p.Add("FromDate", filter.FromDate != null ? filter.FromDate.Value.ToString("dd/MM/yyyy") : String.Empty);
        //    //p.Add("ToDate", filter.ToDate != null ? filter.ToDate.Value.ToString("dd/MM/yyyy") : String.Empty);
        //    p.Add("string_header", headerTitle);
        //    return p;
        //}
        //public DataTable RemoveDistinctRows(DataTable dTable, string colName)
        //{
        //    Hashtable hTable = new Hashtable();
        //    ArrayList duplicateList = new ArrayList();

        //    //Add list of all the unique item value to hashtable, which stores combination of key, value pair.
        //    //And add duplicate item value in arraylist.
        //    foreach (DataRow drow in dTable.Rows)
        //    {
        //        if (hTable.Contains(drow[colName]))
        //            duplicateList.Add(drow);
        //        else
        //            hTable.Add(drow[colName], string.Empty);
        //    }

        //    //Removing a list of duplicate items from datatable.
        //    foreach (DataRow dRow in duplicateList)
        //        dTable.Rows.Remove(dRow);

        //    //Datatable which contains unique records will be return as output.
        //    return dTable;
        //}
        #endregion BC11

        #region BC6
        //public DataSet CreateDataBC6(YmrReportBC6 filterm, DataSet input)
        //{
        //    DataSet dtSet = new DataSet();
        //    DataTable table = new DataTable();
        //    DataTable tableYear = new DataTable();
        //    table = input.Tables[0];
        //    tableYear = input.Tables[1];
        //    DataTable dtdata = new DataTable();
        //    dtdata.Columns.Add("Stt", typeof(string));
        //    dtdata.Columns.Add("MaNhanVien", typeof(string));
        //    dtdata.Columns.Add("Nam", typeof(string));
        //    dtdata.Columns.Add("HoTen", typeof(string));
        //    dtdata.Columns.Add("NgaySinh", typeof(string));
        //    dtdata.Columns.Add("GioiTinh", typeof(string));
        //    dtdata.Columns.Add("BoPhan", typeof(string));
        //    dtdata.Columns.Add("Phong", typeof(string));
        //    dtdata.Columns.Add("To", typeof(string));
        //    dtdata.Columns.Add("NoiLamViec", typeof(string));
        //    //dtdata.Columns.Add("ChucVu", typeof(string));
        //    dtdata.Columns.Add("BoPhanToChuc", typeof(string));
        //    for (int i = 1; i <= 10; i++)
        //    {
        //        for (int j = 1; j <= 10; j++)
        //        {
        //            dtdata.Columns.Add("Nam" + i + "_MaNhomDoiTuongHV" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_NhomDoiTuongHV" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_MaKhoaHoc" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_TenKhoaHoc" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_NgayCapGCN" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_SoGCN" + j, typeof(string));
        //            dtdata.Columns.Add("Nam" + i + "_ThoihanGCN" + j, typeof(string));
        //        }
        //    }

        //    if (table.Rows.Count > 0)
        //    {
        //        var query = (from a in table.AsEnumerable()
        //                     select new
        //                     {
        //                         Stt = a["Stt"],
        //                         MaNhanVien = a["MaNhanVien"],
        //                         Nam = a["Nam"],
        //                         HoTen = a["HoTen"],
        //                         NgaySinh = a["NgaySinh"],
        //                         GioiTinh = a["GioiTinh"],
        //                         BoPhan = a["BoPhan"],
        //                         Phong = a["Phong"],
        //                         To = a["To"],
        //                         NoiLamViec = a["NoiLamViec"],
        //                         //ChucVu = a["ChucVu"],
        //                         BoPhanToChuc = a["BoPhanToChuc"],
        //                         MaNhomDoiTuongHV = a["MaNhomDoiTuongHV"],
        //                         NhomDoiTuongHV = a["NhomDoiTuongHV"],
        //                         MaKhoaHoc = a["MaKhoaHoc"],
        //                         TenKhoaHoc = a["TenKhoaHoc"],
        //                         NgayCapGCN = a["NgayCapGCN"],
        //                         SoGCN = a["SoGCN"],
        //                         ThoihanGCN = a["ThoihanGCN"]

        //                     }).ToList();

        //        foreach (var item1 in query)
        //        {
        //            DataRow row = dtdata.NewRow();
        //            row["Stt"] = item1.Stt;
        //            row["MaNhanVien"] = item1.MaNhanVien;
        //            row["Nam"] = item1.Nam;
        //            row["HoTen"] = item1.HoTen;
        //            row["NgaySinh"] = item1.NgaySinh;
        //            row["GioiTinh"] = item1.GioiTinh;
        //            row["BoPhan"] = item1.BoPhan;
        //            row["Phong"] = item1.Phong;
        //            row["To"] = item1.To;
        //            row["NoiLamViec"] = item1.NoiLamViec;
        //            //row["ChucVu"] = item1.ChucVu;
        //            row["BoPhanToChuc"] = item1.BoPhanToChuc;
        //            dtdata.Rows.Add(row);
        //        }
        //        RemoveDistinctRows(dtdata, "MaNhanVien");
        //        //update du lieu that
        //        int kk = 1;
        //        string nam = String.Empty;
        //        foreach (DataRow row in dtdata.Rows)
        //        {
        //            //for (int ii = 1; ii <= 10; ii++)
        //            //{
        //            string manv = row["MaNhanVien"].ToString();
        //            //ii = 1;
        //            kk = 1;
        //            var lst = query.OrderBy(t => t.MaNhanVien).OrderBy(t => t.Nam).Where(t => t.MaNhanVien.ToString() == manv).ToList();
        //            //var lsy = lst.Select(t => t.Nam).Distinct();
        //            for (int ii = 0; ii < tableYear.Rows.Count; ii++)
        //            {
        //                kk = 1;
        //                foreach (var it in lst.Where(t => t.Nam.ToString() == tableYear.Rows[ii]["year"].ToString()).ToList())
        //                {
        //                    row["Nam" + (ii + 1) + "_MaNhomDoiTuongHV" + kk] = it.MaNhomDoiTuongHV;
        //                    row["Nam" + (ii + 1) + "_NhomDoiTuongHV" + kk] = it.NhomDoiTuongHV;
        //                    row["Nam" + (ii + 1) + "_MaKhoaHoc" + kk] = it.MaKhoaHoc;
        //                    row["Nam" + (ii + 1) + "_TenKhoaHoc" + kk] = it.TenKhoaHoc;
        //                    row["Nam" + (ii + 1) + "_NgayCapGCN" + kk] = it.NgayCapGCN;
        //                    row["Nam" + (ii + 1) + "_SoGCN" + kk] = it.SoGCN;
        //                    row["Nam" + (ii + 1) + "_ThoihanGCN" + kk] = it.ThoihanGCN;
        //                    kk++;
        //                }
        //            }
        //        }

        //    }
        //    dtSet.Tables.Add(dtdata);
        //    return dtSet;
        //}
        //public Dictionary<string, object> CreateDataDicBC6(YmrReportBC6 filter, DataSet input, string headerTitle)
        //{
        //    DataTable tableYear = new DataTable();
        //    tableYear = input.Tables[1];
        //    Dictionary<String, Object> p = new Dictionary<string, object>();
        //    for (int i = 0; i < 10; i++)
        //    {
        //        p.Add("Nam" + (i + 1), i >= tableYear.Rows.Count ? String.Empty : tableYear.Rows[i]["year"].ToString());
        //    }
        //    //p.Add("FromDate", filter.FromDate.Value.ToString("dd/MM/yyyy"));
        //    //p.Add("ToDate", filter.ToDate.Value.ToString("dd/MM/yyyy"));
        //    p.Add("string_header", headerTitle);
        //    return p;
        //}
        #endregion BC6






    }
}
