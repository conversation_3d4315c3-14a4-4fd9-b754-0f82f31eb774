﻿using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;

namespace SSG.API.Keycloak.Template.Extensions
{
    public static class HttpRequestExtensionExtension
    {
        public static string GetAccessToken(this HttpRequest request)
        {
            var accessToken = request.Headers[HeaderNames.Authorization].ToString()
                .Replace("Bearer ", string.Empty)
                .Replace("bearer ", string.Empty);

            return accessToken;
        }
    }
}
