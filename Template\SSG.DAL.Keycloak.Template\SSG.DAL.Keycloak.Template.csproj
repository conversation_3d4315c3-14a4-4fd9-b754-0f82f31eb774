﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AssemblyName>SSG.DAL.Keycloak.Template</AssemblyName>
    <PackageId>SSG.DAL.Yamaha_Training</PackageId>    
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <LangVersion>7.1</LangVersion>
    <RootNamespace>SSG.DAL.Keycloak.Template</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Interfaces\INotificationRepository.cs" />
    <Compile Remove="Repositories\NotificationRepository.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Repositories\SHousingRepository.cs~RF3a78c445.TMP" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.3.100.1" />
    <PackageReference Include="BarcodeLib" Version="2.2.6" />
    <PackageReference Include="Dapper" Version="2.0.35" />
    <PackageReference Include="Dapper.ParameterExtensions" Version="2018.12.7.1" />
    <PackageReference Include="Elasticsearch.Net" Version="7.5.1" />
    <PackageReference Include="Elasticsearch.Net.Aws" Version="2.4.0" />
    <PackageReference Include="FirebaseAuthentication.net" Version="3.4.0" />
    <PackageReference Include="FirebaseDatabase.net" Version="4.0.4" />

    <PackageReference Include="Microsoft.Extensions.Configuration" Version="2.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="2.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="2.0.1" />
    <PackageReference Include="NEST" Version="7.5.1" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="2.12.0-beta2" />
    <PackageReference Include="System.Data.Common" Version="4.3.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.4.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\sunshine-common\SSG.Model\SSG.Model.csproj" />
  </ItemGroup>

</Project>
