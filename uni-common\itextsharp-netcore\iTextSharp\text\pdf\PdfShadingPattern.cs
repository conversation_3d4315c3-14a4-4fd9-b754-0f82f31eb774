/*
    This file is part of the iText (R) project.
    Copyright (c) 1998-2020 iText Group NV
    Authors: <AUTHORS>

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU Affero General Public License version 3
    as published by the Free Software Foundation with the addition of the
    following permission added to Section 15 as permitted in Section 7(a):
    FOR ANY PART OF THE COVERED WORK IN WHICH THE COPYRIGHT IS OWNED BY
    ITEXT GROUP. ITEXT GROUP DISCLAIMS THE WARRANTY OF NON INFRINGEMENT
    OF THIRD PARTY RIGHTS
    
    This program is distributed in the hope that it will be useful, but
    WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY
    or FITNESS FOR A PARTICULAR PURPOSE.
    See the GNU Affero General Public License for more details.
    You should have received a copy of the GNU Affero General Public License
    along with this program; if not, see http://www.gnu.org/licenses or write to
    the Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
    Boston, MA, 02110-1301 USA, or download the license from the following URL:
    http://itextpdf.com/terms-of-use/
    
    The interactive user interfaces in modified source and object code versions
    of this program must display Appropriate Legal Notices, as required under
    Section 5 of the GNU Affero General Public License.
    
    In accordance with Section 7(b) of the GNU Affero General Public License,
    a covered work must retain the producer line in every PDF that is created
    or manipulated using iText.
    
    You can be released from the requirements of the license by purchasing
    a commercial license. Buying such a license is mandatory as soon as you
    develop commercial activities involving the iText software without
    disclosing the source code of your own applications.
    These activities include: offering paid services to customers as an ASP,
    serving PDFs on the fly in a web application, shipping iText with a closed
    source product.
    
    For more information, please contact iText Software Corp. at this
    address: <EMAIL>
 */
using System;
using iTextSharp.text.error_messages;

namespace iTextSharp.text.pdf {

    /** Implements the shading pattern dictionary.
     *
     * <AUTHOR> Soares
     */
    public class PdfShadingPattern : PdfDictionary {

        protected PdfShading shading;
    
        protected PdfWriter writer;
    
        protected float[] matrix = {1, 0, 0, 1, 0, 0};
    
        protected PdfName patternName;

        protected PdfIndirectReference patternReference;

        /** Creates new PdfShadingPattern */
        public PdfShadingPattern(PdfShading shading) {
            writer = shading.Writer;
            Put(PdfName.PATTERNTYPE, new PdfNumber(2));
            this.shading = shading;
        }
        
        internal PdfName PatternName {
            get {
                return patternName;
            }
        }

        internal PdfName ShadingName {
            get {
                return shading.ShadingName;
            }
        }
    
        internal PdfIndirectReference PatternReference {
            get {
                if (patternReference == null)
                    patternReference = writer.PdfIndirectReference;
                return patternReference;
            }
        }
    
        internal PdfIndirectReference ShadingReference {
            get {
                return shading.ShadingReference;
            }
        }
    
        internal int Name {
            set {
                patternName = new PdfName("P" + value);
            }
        }
    
        virtual public void AddToBody() {
            Put(PdfName.SHADING, ShadingReference);
            Put(PdfName.MATRIX, new PdfArray(matrix));
            writer.AddToBody(this, PatternReference);
        }
    
        virtual public float[] Matrix {
            get {
                return matrix;
            }

            set {
                if (value.Length != 6)
                    throw new Exception(MessageLocalization.GetComposedMessage("the.matrix.size.must.be.6"));
                this.matrix = value;
            }
        }
    
        virtual public PdfShading Shading {
            get {
                return shading;
            }
        }
    
        internal ColorDetails ColorDetails {
            get {
                return shading.ColorDetails;
            }
        }

    }
}
