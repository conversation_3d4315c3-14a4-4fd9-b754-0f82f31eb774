﻿using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentEmail.Core;
using FluentEmail.Mailgun;
using RestSharp;
using RestSharp.Authenticators;
using SSG.Model.Email;

namespace SSG.BLL.Keycloak.Template.BusinessService.HelperService
{
    public class MailgunSendService
    {
        private readonly string _apiKey;
        private readonly string _domainName;

        public MailgunSendService(string domainName, string apiKey)
        {
            _domainName = domainName;
            _apiKey = apiKey;
        }

        public EmailResponse Send(IFluentEmail email, CancellationToken? token = null)
        {
            return SendAsync(email, token).GetAwaiter().GetResult();
        }

        private string GetRecipientVariables(IFluentEmail email)
        {
            var listMail = email.Data.ToAddresses.Select(x => "\"" + x.EmailAddress + "\":\"\"");
            return "{" + string.Join(", ", listMail) + "}";
        }
        public Task<EmailResponse> SendAsync(IFluentEmail email, CancellationToken? token = null, bool sendBatch = false)
        {

            var client = new RestClient($"https://api.mailgun.net/v3/{_domainName}");
            client.Authenticator = new HttpBasicAuthenticator("api", _apiKey);

            var request = new RestRequest("messages", Method.POST);
            request.AddParameter("from", $"{email.Data.FromAddress.Name} <{email.Data.FromAddress.EmailAddress}>");
            email.Data.ToAddresses.ForEach(x => {
                request.AddParameter("to", $"{x.Name} <{x.EmailAddress}>");
            });
            email.Data.CcAddresses.ForEach(x => {
                request.AddParameter("cc", $"{x.Name} <{x.EmailAddress}>");
            });
            email.Data.BccAddresses.ForEach(x => {
                request.AddParameter("bcc", $"{x.Name} <{x.EmailAddress}>");
            });
            request.AddParameter("subject", email.Data.Subject);

            request.AddParameter(email.Data.IsHtml ? "html" : "text", email.Data.Body);

            if (!string.IsNullOrEmpty(email.Data.PlaintextAlternativeBody))
            {
                request.AddParameter("text", email.Data.PlaintextAlternativeBody);
            }

            if (email.Data.Attachments.Any())
            {
                request.AlwaysMultipartFormData = true;
            }
            email.Data.Attachments.ForEach(x =>
            {
                request.AddFile("attachment", StreamWriter(x.Data), x.Filename, x.Data.Length, x.ContentType);
            });
            if (sendBatch)
            {
                request.AddParameter("recipient-variables", GetRecipientVariables(email));
            }
            return Task.Run(() =>
            {
                var t = new TaskCompletionSource<EmailResponse>();

                var handle = client.ExecuteAsync<MailgunResponse>(request, response =>
                {
                    var result = new EmailResponse();
                    if (string.IsNullOrEmpty(response.Data.Id))
                    {
                        result.ErrorMessages.Add(response.Data.Message);
                    }
                    result.HttpStatusCode = response.StatusCode;
                    result.MessageId = response.Data.Id;
                    t.TrySetResult(result);
                    
                });

                return t.Task;
            });
        }

        private Action<Stream> StreamWriter(Stream stream)
        {
            return s =>
            {
                stream.CopyTo(s);
                stream.Dispose();
            };
        }
    }
}