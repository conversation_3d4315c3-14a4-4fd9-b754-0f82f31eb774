﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Amazon.SimpleEmail.Model;
using SSG.Model;
using SSG.Model.Api;
using SSG.Model.Email;
using SSG.Model.SMS;
using SSG.Model.SMS.ViewModel;

namespace SSG.BLL.BusinessServiceInterfaces
{

    /// <summary>
    /// 
    /// </summary>
    /// Author: taint
    /// CreatedDate: 16/11/2016 2:01 PM
    public interface INotificationService
    {

        Task<int> AddNewSMS(List<SMSEntity> sms, string clientId, string userId);
        ResponseList<IEnumerable<SMS>> GetSMS(PagingParams pagingParams, long? sendId = null, long? id = null);
        Task<int> UpdateSMS(List<SMSEntity> sms, string clientId, string userId);

        Task<long> AddNewSMSGroup(SMSGroup smsGroup, string clientId, string userId);
        ResponseList<IEnumerable<SMSGroup>> GetSMSGroup(PagingParams pagingParams, long? sendId = null);
        Task<int> UpdateSMSGroup(SMSGroup smsGroup, string clientId, string userId);

        Task<int> UpdateTemplate(List<SmsTemplate> sms, long? userId = null);
        Task<int> AddNewTemplate(List<SmsTemplate> sms, long? userId = null);
        IEnumerable<SmsTemplate> GetSmsTemplates(int? templateId = null);
        Task<int> DeleteTemplates(List<SmsTemplate> smsTemplates);

        Task<int> UpdateTemplateType(List<SmsTemplateType> sms, long? userId = null);
        Task<int> AddNewTemplateType(List<SmsTemplateType> sms, long? userId = null);
        IEnumerable<SmsTemplateType> GetSmsTemplateTypes(int? typeId = null);
        Task<int> DeleteTemplateTypes(List<SmsTemplateType> smsTemplateTypes);

        ResponseList<IEnumerable<SmsCustomer>> GetCustomers(PagingParams pagingParams, int? categoryId = null,
            int? cusId = null);

        Task<int> UpdateCustomer(List<SmsCustomer> customers, long? userId = null);
        Task<int> AddNewCustomer(List<SmsCustomer> customers, long? userId = null);
        Task<int> DeleteCustomers(List<SmsCustomer> customers);

        IEnumerable<SmsCustomerCategory> GetCustomerCategory();
        Task<int> UpdateCustomerCategory(List<SmsCustomerCategory> customers, long? userId = null);
        Task<int> AddNewCustomerCategory(List<SmsCustomerCategory> customers, long? userId = null);
        Task<int> DeleteCustomerCategory(int categoryId, bool? delCustomer);
        SmsCustomerCategory GetCustomerCategoryDetail(int categoryId);
        Task<BaseResponse<List<Item>>> SendSms(CreateSmsModel sms, string clientId, string userId);

        Task<EmailResponse> SendEmail(CreateEmailModel emailModel, string clientId, string userId);
        Task<int> AddNewEmail(List<string> sms, long sendId, string clientId, string userId);
        ResponseList<IEnumerable<EmailGroup>> GetEmailGroup(PagingParams pagingParams, long sendId);
        ResponseList<IEnumerable<EmailEntity>> GetEmail(PagingParams pagingParams, long sendId, long id);
    }
}
