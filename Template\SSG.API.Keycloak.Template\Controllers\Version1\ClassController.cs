﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using SSG.API.Keycloak.Template.Extensions;
using SSG.BLL.Keycloak.Template.BusinessServiceInterfaces;
using SSG.Model;
using SSG.Model.Api;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace SSG.API.Keycloak.Template.Controllers.Version1
{
    /// <inheritdoc />
    [Authorize]
    [Route("api/v1/class/[action]")]
    [ApiController]
    public class ClassController : SSGController
    {
        private readonly IClassService _service;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="service"></param>
        public ClassController(ILoggerFactory logger, IClassService service) : base(logger)
        {
            _service = service;
        }
        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="query"></param>
        ///// <returns></returns>
        //[HttpGet]
        //public async Task<BaseResponse<YmClassPage>> GetPage([FromQuery] FilterBase query)
        //{
        //    try
        //    {
        //        var rs = await _service.GetPageAsync(query);
        //        var rp = GetResponse(ApiResult.Success, rs);
        //        return rp;
        //    }
        //    catch (Exception e)
        //    {
        //        _logger.LogError($"{e}");
        //        var rp = new BaseResponse<YmClassPage>(ApiResult.Error, e.Message);
        //        return rp;
        //    }
        //}
        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="id"></param>
        ///// <returns></returns>
        //[HttpGet]
        //public async Task<BaseResponse<YmClassInfo>> GetInfo([FromQuery] Guid? id)
        //{
        //    try
        //    {
        //        var rs = await _service.GetInfoAsync(this.User.GetUserId(), id);
        //        var sCode = rs != null ? ApiResult.Success : ApiResult.Error;
        //        var response = GetResponse(sCode, rs);
        //        return response;
        //    }
        //    catch (Exception e)
        //    {
        //        _logger.LogError($"{e}");
        //        var rp = new BaseResponse<YmClassInfo>(ApiResult.Error, e.Message);
        //        return rp;
        //    }
        //}

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="info"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public async Task<BaseResponse<string>> SetInfo([FromBody] YmClassInfo info)
        //{
        //    try
        //    {
        //        var rs = await _service.SetInfoAsync(this.User.GetUserId(), info);
        //        var rp = GetResponse(rs.valid ? ApiResult.Success : ApiResult.Error, "", rs.messages);
        //        return rp;
        //    }
        //    catch (Exception e)
        //    {
        //        _logger.LogError(e.ToString());
        //        return GetResponse(ApiResult.Error, "", e.Message);
        //    }
        //}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> Delete([FromQuery, Required] Guid? id)
        {
            try
            {
                var rs = await _service.DeleteAsync(this.User.GetUserId(), id);
                var rp = GetResponse(rs.valid ? ApiResult.Success : ApiResult.Error, "");
                rp.Message = rs.messages;
                return rp;
            }
            catch (Exception e)
            {
                _logger.LogError(e.ToString());
                return GetResponse<string>(ApiResult.Error, null, e.Message);
            }
        }
        ///// <summary>
        ///// import sinh vien vao lop
        ///// </summary>
        ///// <param name="formFile"></param>
        //[HttpPost]
        //public async Task<BaseResponse<ImportClassResponse>> Import(IFormFile formFile)
        //{
        //    var result = new BaseResponse<ImportClassResponse>();
        //    try
        //    {
        //        if (formFile == null || formFile.Length <= 0)
        //        {
        //            result.AddError(ApiResult.Error, "Chưa có tệp được chọn");
        //            result.SetStatus(ApiResult.Error);
        //            return result;
        //        }

        //        if (!Path.GetExtension(formFile.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
        //        {
        //            result.AddError(ApiResult.Error, "Định dạng tệp không được hỗ trợ, chỉ hỗ trợ tệp .xlsx");
        //            result.SetStatus(ApiResult.Error);
        //            return result;
        //        }
        //        var list = new List<ClassImport>();
        //        using (var stream = new MemoryStream())
        //        {
        //            await formFile.CopyToAsync(stream);

        //            using (var package = new ExcelPackage(stream))
        //            {
        //                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        //                var worksheet = package.Workbook.Worksheets[0];

        //                var rowCount = worksheet.Dimension.Rows;
        //                //đưa toàn bộ dữ liệu vào list

        //                if (worksheet.Cells[3, 1].Value != null && worksheet.Cells[3,1].Value.ToString() != "Course code")
        //                {
        //                    result.AddError(ApiResult.Error, "Định dạng báo cáo không hợp lệ, dữ liệu bắt dầu từ dòng thứ 6");
        //                    result.SetStatus(ApiResult.Error);
        //                    return result;
        //                }
        //                for (int row = 4; row <= rowCount; row++)
        //                {
        //                    if (worksheet.Cells[row, 1].Value == null) break;
        //                    try
        //                    {
        //                        list.Add(new ClassImport
        //                        {
        //                            // organizer_code = worksheet.Cells[row, 1].Value != null ? worksheet.Cells[row, 1].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            // organizer_name = worksheet.Cells[row, 2].Value != null ? worksheet.Cells[row, 2].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            // trainee_code = worksheet.Cells[row, 3].Value != null ? worksheet.Cells[row, 3].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            // trainee_name = worksheet.Cells[row, 4].Value != null ? worksheet.Cells[row, 4].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            course_code = worksheet.Cells[row, 1].Value != null ? worksheet.Cells[row, 1].Value?.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            // course_name = worksheet.Cells[row, 6].Value != null ? worksheet.Cells[row, 6].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            class_code = worksheet.Cells[row, 2].Value != null ? worksheet.Cells[row, 2].Value?.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            class_name = worksheet.Cells[row, 3].Value != null ? worksheet.Cells[row, 3].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            class_en_name = worksheet.Cells[row, 4].Value != null ? worksheet.Cells[row, 4].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            class_short_name = worksheet.Cells[row, 5].Value != null ? worksheet.Cells[row, 5].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
                                    
        //                            training_type = worksheet.Cells[row, 6].Value != null ? worksheet.Cells[row,6].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            training_type_name = worksheet.Cells[row, 7].Value != null ? worksheet.Cells[row, 7].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
                                    
        //                            teacher = worksheet.Cells[row, 8].Value != null ? worksheet.Cells[row, 8].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            trainer_info = worksheet.Cells[row, 9].Value != null ? worksheet.Cells[row, 9].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
                                    
        //                            training_place = worksheet.Cells[row, 10].Value != null ? worksheet.Cells[row, 10].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            training_agent = worksheet.Cells[row, 11].Value != null ? worksheet.Cells[row, 11].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
                                    
        //                            start_date = worksheet.Cells[row, 12].Value != null ? worksheet.Cells[row, 12].Text.Trim().Replace(Environment.NewLine, "") : "",
        //                            end_date = worksheet.Cells[row, 13].Value != null ? worksheet.Cells[row, 13].Text.Trim().Replace(Environment.NewLine, "") : "",
                                    
        //                            number_of_student = worksheet.Cells[row, 14].Value != null ? worksheet.Cells[row, 14].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            duration = worksheet.Cells[row, 15].Value != null ? worksheet.Cells[row, 15].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            cost_by_class = worksheet.Cells[row, 16].Value != null ? worksheet.Cells[row, 16].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            // cost_by_trainee = worksheet.Cells[row, 13].Value != null ? worksheet.Cells[row, 13].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                            // cost_by_course = worksheet.Cells[row, 15].Value != null ? worksheet.Cells[row, 15].Value.ToString().Trim().Replace(Environment.NewLine, "") : "",
        //                        });
        //                    }
        //                    catch (Exception ex)
        //                    {
        //                        result.AddError(ApiResult.Error, "Định dạng dữ liệu chưa hợp lệ, xin vui lòng kiểm tra lại tại dòng " + (row).ToString());
        //                        result.SetStatus(ApiResult.Error);
        //                        return result;
        //                    }
        //                }
        //                //chuyển dữ liệu sang xml
        //                var xml = new XElement("ClassDocument",
        //                                        from p in list
        //                                        select new XElement("Class",
        //                                                    // new XElement("organizer_code", p.organizer_code),
        //                                                    // new XElement("organizer_name", p.organizer_name),
        //                                                    // new XElement("trainee_code", p.trainee_code),
        //                                                    // new XElement("trainee_name", p.trainee_name),
        //                                                    new XElement("course_code", p.course_code),
        //                                                    // new XElement("course_name", p.course_name),
        //                                                    new XElement("class_code", p.class_code),
        //                                                    new XElement("class_name", p.class_name),
        //                                                    new XElement("class_en_name", p.class_en_name),
        //                                                    new XElement("class_short_name", p.class_short_name),
        //                                                    new XElement("training_type", p.training_type),
        //                                                    new XElement("training_type_name", p.training_type_name),
        //                                                    new XElement("teacher", p.teacher),
        //                                                    new XElement("trainer_info", p.trainer_info),
        //                                                    new XElement("training_place", p.training_place),
        //                                                    new XElement("training_agent", p.training_agent),
        //                                                    new XElement("start_date", p.start_date),
        //                                                    new XElement("end_date", p.end_date),
        //                                                    new XElement("number_of_student", p.number_of_student),
        //                                                    new XElement("duration", p.duration),
        //                                                    new XElement("cost_by_class", p.cost_by_class)

        //                                        )).ToString();
        //                // thực hiện việc import
        //                result = await _service.Import(this.User.GetUserId(), xml);
        //                result.SetStatus(ApiResult.Success, "Thực hiện thành công");
        //                return result;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        result.AddError(ApiResult.Error, "Định dạng dữ liệu chưa hợp lệ, xin vui lòng kiểm tra lại");
        //        result.SetStatus(ApiResult.Error);
        //        return result;
        //    }
        //}
    }
}
