﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SSG.Model;
using SSG.Model.Email;
using SSG.Model.SMS;

namespace SSG.DAL.Interfaces
{
    
    public interface INotificationRepository
    {
        Task<int> AddNewSMS(List<SMSEntity> sms, string clientId, string userId);
        Task<int> UpdateSMS(List<SMSEntity> sms, string clientId, string userId);
        ResponseList<IEnumerable<SMS>> GetSMS(PagingParams pagingParams, long? sendId = null, long? id = null);
        Task<long> AddNewSMSGroup(SMSGroup smsGroup, string clientId, string userId);
        ResponseList<IEnumerable<SMSGroup>> GetSMSGroup(PagingParams pagingParams, long? sendId = null);
        Task<int> UpdateSMSGroup(SMSGroup smsGroup, string clientId, string userId);

        Task<int> UpdateTemplate(List<SmsTemplate> sms, long? userId);
        Task<int> AddNewTemplate(List<SmsTemplate> sms, long? userId);
        IEnumerable<SmsTemplate> GetSmsTemplates(int? templateId);
        Task<int> DeleteTemplates(List<SmsTemplate> smsTemplates);

        Task<int> UpdateTemplateType(List<SmsTemplateType> sms, long? userId);
        Task<int> AddNewTemplateType(List<SmsTemplateType> sms, long? userId);
        IEnumerable<SmsTemplateType> GetSmsTemplateTypes(int? typeId);
        Task<int> DeleteTemplateTypes(List<SmsTemplateType> smsTemplateTypes);

        ResponseList<IEnumerable<SmsCustomer>> GetCustomers(PagingParams pagingParams, int? categoryId = null,
            int? cusId = null);
        Task<int> DeleteCustomers(List<SmsCustomer> customers);
        Task<int> UpdateCustomer(List<SmsCustomer> customers, long? userId);
        Task<int> AddNewCustomer(List<SmsCustomer> customers, long? userId);
        
        IEnumerable<SmsCustomerCategory> GetCustomerCategory();
        Task<int> UpdateCustomerCategory(List<SmsCustomerCategory> customers, long? userId);
        Task<int> AddNewCustomerCategory(List<SmsCustomerCategory> customers, long? userId);
        Task<int> DeleteCustomerCategory(int categoryId, bool? delCustomer);
        SmsCustomerCategory GetCustomerCategoryDetail(int categoryId);
        Task<long> AddNewEmailGroup(EmailGroup smsGroup, string clientId, string userId);
        Task<int> AddNewEmail(List<string> sms, long sendId, string clientId, string userId);
        ResponseList<IEnumerable<EmailGroup>> GetEmailGroup(PagingParams pagingParams, long sendId);
        ResponseList<IEnumerable<EmailEntity>> GetEmail(PagingParams pagingParams, long sendId, long id);
    }
}
