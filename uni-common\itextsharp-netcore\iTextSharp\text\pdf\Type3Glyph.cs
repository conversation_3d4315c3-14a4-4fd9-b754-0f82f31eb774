/*
    This file is part of the iText (R) project.
    Copyright (c) 1998-2020 iText Group NV
    Authors: <AUTHORS>

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU Affero General Public License version 3
    as published by the Free Software Foundation with the addition of the
    following permission added to Section 15 as permitted in Section 7(a):
    FOR ANY PART OF THE COVERED WORK IN WHICH THE COPYRIGHT IS OWNED BY
    ITEXT GROUP. ITEXT GROUP DISCLAIMS THE WARRANTY OF NON INFRINGEMENT
    OF THIRD PARTY RIGHTS
    
    This program is distributed in the hope that it will be useful, but
    WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY
    or FITNESS FOR A PARTICULAR PURPOSE.
    See the GNU Affero General Public License for more details.
    You should have received a copy of the GNU Affero General Public License
    along with this program; if not, see http://www.gnu.org/licenses or write to
    the Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
    Boston, MA, 02110-1301 USA, or download the license from the following URL:
    http://itextpdf.com/terms-of-use/
    
    The interactive user interfaces in modified source and object code versions
    of this program must display Appropriate Legal Notices, as required under
    Section 5 of the GNU Affero General Public License.
    
    In accordance with Section 7(b) of the GNU Affero General Public License,
    a covered work must retain the producer line in every PDF that is created
    or manipulated using iText.
    
    You can be released from the requirements of the license by purchasing
    a commercial license. Buying such a license is mandatory as soon as you
    develop commercial activities involving the iText software without
    disclosing the source code of your own applications.
    These activities include: offering paid services to customers as an ASP,
    serving PDFs on the fly in a web application, shipping iText with a closed
    source product.
    
    For more information, please contact iText Software Corp. at this
    address: <EMAIL>
 */
using System;
using iTextSharp.text.error_messages;

namespace iTextSharp.text.pdf {

    /**
    * The content where Type3 glyphs are written to.
    */
    public sealed class Type3Glyph : PdfContentByte {

        private PageResources pageResources;
        private bool colorized;
        
        private Type3Glyph() : base(null) {
        }
        
        internal Type3Glyph(PdfWriter writer, PageResources pageResources, float wx, float llx, float lly, float urx, float ury, bool colorized) : base(writer) {
            this.pageResources = pageResources;
            this.colorized = colorized;
            if (colorized) {
                content.Append(wx).Append(" 0 d0\n");
            }
            else {
                content.Append(wx).Append(" 0 ").Append(llx).Append(' ').Append(lly).Append(' ').Append(urx).Append(' ').Append(ury).Append(" d1\n");
            }
        }
        
        internal override PageResources PageResources {
            get {
                return pageResources;
            }
        }
        
        public override void AddImage(Image image, float a, float b, float c, float d, float e, float f, bool inlineImage) {
            if (!colorized && (!image.IsMask() || !(image.Bpc == 1 || image.Bpc > 0xff)))
                throw new DocumentException(MessageLocalization.GetComposedMessage("not.colorized.typed3.fonts.only.accept.mask.images"));
            base.AddImage(image, a, b, c, d, e, f, inlineImage);
        }

        public PdfContentByte GetDuplicate() {
            Type3Glyph dup = new Type3Glyph();
            dup.writer = writer;
            dup.pdf = pdf;
            dup.pageResources = pageResources;
            dup.colorized = colorized;
            return dup;
        }    
    }
}
