﻿using System.ComponentModel.DataAnnotations;

namespace SSG.API.Keycloak.Template.Authorization
{
    public class Resource
    {
        [Display(Description = "<PERSON>ớp học")]
        public const string Class = "resource:api:class";

        [Display(Description = "<PERSON>ọc viên trong lớp học")]
        public const string ClassStudent = "resource:api:class-student";

        [Display(Description = "Cấu hình")]
        public const string Config = "resource:api:config";

        [Display(Description = "Khóa học")]
        public const string Course = "resource:api:course";

        [Display(Description = "Tổ chức")]
        public const string Organizer = "resource:api:organizer";

        [Display(Description = "Báo cáo")]
        public const string Report = "resource:api:report";

        [Display(Description = "Nhóm đối tượng học viên")]
        public const string Trainee = "resource:api:trainee";

        [Display(Description = "<PERSON><PERSON><PERSON> thức đạo tạo")]
        public const string TypeTraining = "resource:api:type";

        [Display(Description = "Người dùng")]
        public const string User = "resource:api:user";
    }
}
